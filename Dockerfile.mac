# Mac优化版Dockerfile - 支持Apple Silicon (M1/M2/M3) 和 Intel芯片
# 使用多阶段构建优化最终镜像大小

# 构建阶段
FROM --platform=$BUILDPLATFORM node:22-bullseye AS builder

# 设置构建参数
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# Mac优化：设置npm配置并安装所有依赖（包括dev依赖用于构建）
RUN npm config set registry https://registry.npmjs.org/ \
    && npm config set fetch-retry-mintimeout 20000 \
    && npm config set fetch-retry-maxtimeout 120000 \
    && npm config set fund false \
    && npm config set audit false \
    # 针对Apple Silicon优化编译参数
    && if [ "$TARGETARCH" = "arm64" ]; then \
        export CC=gcc CXX=g++ AR=ar STRIP=strip OBJCOPY=objcopy; \
    fi \
    && npm ci \
    && npm cache clean --force

# 复制源代码
COPY . .

# 编译TypeScript，为Mac优化内存使用
RUN NODE_OPTIONS="--max-old-space-size=2048" npm run build

# 运行阶段
FROM --platform=$BUILDPLATFORM node:22-bullseye AS runtime

# 设置构建参数
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH

# 安装基础依赖和Puppeteer所需的系统库
RUN apt-get update && apt-get install -y \
    # 基础系统库
    ca-certificates \
    fonts-liberation \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgcc1 \
    libgconf-2-4 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    # Chrome/Chromium依赖
    libdrm2 \
    libxkbcommon0 \
    libgbm1 \
    libnss3 \
    # 工具和字体
    lsb-release \
    wget \
    gnupg \
    curl \
    unzip \
    dumb-init \
    fonts-noto-cjk \
    fonts-noto \
    fonts-dejavu-core \
    fontconfig \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 安装适合平台的Chromium
RUN if [ "$TARGETARCH" = "arm64" ]; then \
        # Apple Silicon (M1/M2/M3) 架构
        echo "Installing Chromium for ARM64 (Apple Silicon)"; \
        apt-get update && apt-get install -y chromium --no-install-recommends; \
    else \
        # Intel x86_64 架构
        echo "Installing Chromium for x86_64 (Intel)"; \
        apt-get update && apt-get install -y chromium --no-install-recommends; \
    fi \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 只安装生产依赖
RUN npm config set registry https://registry.npmjs.org/ \
    && npm config set fetch-retry-mintimeout 20000 \
    && npm config set fetch-retry-maxtimeout 120000 \
    && npm config set fund false \
    && npm config set audit false \
    && npm ci --only=production --no-optional \
    && npm cache clean --force

# 从构建阶段复制编译后的应用
COPY --from=builder /app/dist ./dist

# 复制其他必要文件
COPY --from=builder /app/.env* ./
COPY --from=builder /app/README*.md ./

# Mac优化环境变量
ENV NODE_ENV=production
ENV TZ=Asia/Shanghai

# Puppeteer配置 - Mac优化
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
ENV PUPPETEER_CACHE_DIR=/app/.cache/puppeteer

# Mac Docker优化的Puppeteer参数
ENV PUPPETEER_ARGS="--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process,VizDisplayCompositor --disable-site-isolation-trials --disable-dev-shm-usage --disable-gpu --disable-software-rasterizer --js-flags=--max-old-space-size=1024 --memory-pressure-off --max_old_space_size=1024"

# Mac Docker内存优化
ENV NODE_OPTIONS="--max-old-space-size=1024 --max-semi-space-size=128"

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非root用户（Mac安全最佳实践）
RUN groupadd -r appuser && useradd -r -g appuser -s /bin/bash appuser \
    && chown -R appuser:appuser /app

# 创建必要目录
RUN mkdir -p /app/logs /app/screenshots /app/emails /app/.cache \
    && chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# 使用dumb-init作为入口点，处理僵尸进程
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["npm", "start"]
