/**
 * Default configuration values
 * These can be overridden by environment variables or .env file
 */

export default {
    // General settings
    environment: process.env.NODE_ENV || 'development',
    logLevel: process.env.LOG_LEVEL || 'info',

    // Email provider settings
    emailProvider: {
        // Which email provider to use: 'microsoft', 'gmail', 'custom', or 'microsoft-alias'
        type: process.env.EMAIL_PROVIDER_TYPE || 'microsoft',
        email: process.env.EMAIL_ADDRESS || '',
        password: process.env.EMAIL_PASSWORD || '',
        clientId: process.env.CLIENT_ID || '',
        clientSecret: process.env.CLIENT_SECRET || '',
        refreshToken: process.env.REFRESH_TOKEN || '',
        host: process.env.EMAIL_HOST || '',
        port: process.env.EMAIL_PORT ? parseInt(process.env.EMAIL_PORT, 10) : 993,
        // 微软邮箱别名配置
        useAlias: process.env.USE_MICROSOFT_ALIAS === 'true',
        aliasPrefix: process.env.MICROSOFT_ALIAS_PREFIX || '',
        aliasRandomLength: process.env.MICROSOFT_ALIAS_RANDOM_LENGTH ? parseInt(process.env.MICROSOFT_ALIAS_RANDOM_LENGTH, 10) : 5
    },

    // Puppeteer settings
    browser: {
        headless: process.env.BROWSER_HEADLESS !== 'false',
        screenshotsDir: process.env.SCREENSHOTS_DIR || 'screenshots',
        userAgent: process.env.USER_AGENT || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        // 流量优化配置
        optimizeBandwidth: process.env.OPTIMIZE_BANDWIDTH === 'true',
        // 代理配置
        proxy: {
            enabled: process.env.PROXY_ENABLED === 'true',
            server: process.env.PROXY_SERVER || '',
            username: process.env.PROXY_USERNAME || '',
            password: process.env.PROXY_PASSWORD || '',
            // 支持不同类型的代理
            type: process.env.PROXY_TYPE || 'http', // http, https, socks5
        }
    },

    // Debug settings - 调试信息保存控制
    debug: {
        saveScreenshots: process.env.SAVE_SCREENSHOTS === 'true', // 默认不保存截图，设置为true启用
        saveEmails: process.env.SAVE_EMAILS === 'true', // 默认不保存邮件，设置为true启用
        saveDebugLogs: process.env.SAVE_DEBUG_LOGS !== 'false', // 默认保存调试日志，设置为false禁用
    },

    // Registration settings
    registration: {
        retryAttempts: parseInt(process.env.RETRY_ATTEMPTS || '3', 10),
        delayBetweenAttempts: parseInt(process.env.DELAY_BETWEEN_ATTEMPTS || '10000', 10), // 10 seconds
        verificationTimeout: parseInt(process.env.VERIFICATION_TIMEOUT || '300000', 10), // 5 minutes
        maxVerificationAttempts: parseInt(process.env.MAX_VERIFICATION_ATTEMPTS || '10', 10),
        defaultCountry: process.env.DEFAULT_COUNTRY || 'US',
        // 批量注册相关设置
        mode: process.env.REGISTRATION_MODE || 'single', // 注册模式：single或batch
        credentialsFile: process.env.REGISTRATION_CREDENTIALS_FILE || 'accounts.txt', // 凭证文件路径
        outputFile: process.env.REGISTRATION_OUTPUT_FILE || 'api_keys.txt', // 输出文件路径
        maxConcurrent: parseInt(process.env.REGISTRATION_MAX_CONCURRENT || '1', 10), // 最大并行数
        delayBetweenRegistrations: parseInt(process.env.DELAY_BETWEEN_REGISTRATIONS || '60000', 10) // 注册间隔时间
    },

    // Output settings
    output: {
        saveKeysToFile: process.env.SAVE_KEYS_TO_FILE !== 'false',
        keysFile: process.env.KEYS_FILE || 'api_keys.json'
    },

    // User data settings (for registration)
    user: {
        firstName: process.env.USER_FIRST_NAME || '',
        lastName: process.env.USER_LAST_NAME || '',
        dateOfBirth: {
            day: process.env.USER_DOB_DAY || '',
            month: process.env.USER_DOB_MONTH || '',
            year: process.env.USER_DOB_YEAR || ''
        },
        country: process.env.USER_COUNTRY || 'US'
    },

    // Microsoft email settings
    microsoft: {
        clientId: process.env.MICROSOFT_CLIENT_ID || process.env.CLIENT_ID,
        pop3Server: process.env.MICROSOFT_POP3_SERVER || 'outlook.office365.com',
        pop3Port: parseInt(process.env.MICROSOFT_POP3_PORT || '995', 10)
    },

    // Gmail settings
    gmail: {
        clientId: process.env.GMAIL_CLIENT_ID || process.env.CLIENT_ID,
        clientSecret: process.env.GMAIL_CLIENT_SECRET || process.env.CLIENT_SECRET
    },

    // Custom email settings
    customEmail: {
        host: process.env.CUSTOM_EMAIL_HOST || process.env.EMAIL_HOST,
        port: parseInt(process.env.CUSTOM_EMAIL_PORT || process.env.EMAIL_PORT || '993', 10),
        useTLS: process.env.CUSTOM_EMAIL_USE_TLS !== 'false'
    },

    // System submission settings
    systemSubmission: {
        url: process.env.SYSTEM_SUBMISSION_URL,
        authorization: process.env.SYSTEM_SUBMISSION_AUTHORIZATION,
        headers: {
            'User-Agent': process.env.SYSTEM_SUBMISSION_USER_AGENT,
            'Content-Type': process.env.SYSTEM_SUBMISSION_CONTENT_TYPE,
            'Accept': process.env.SYSTEM_SUBMISSION_ACCEPT
        },
        channelConfig: {
            name: process.env.CHANNEL_NAME,
            type: parseInt(process.env.CHANNEL_TYPE || '1', 10),
            billing_type: parseInt(process.env.CHANNEL_BILLING_TYPE || '1', 10),
            group: process.env.CHANNEL_GROUP,
            models: process.env.CHANNEL_MODELS,
            base_url: "",
            config: "{}",
            channel_group_id: parseInt(process.env.CHANNEL_GROUP_ID || '11', 10),
            sort: 0,
            weight: 0,
            function_call_enabled: true,
            image_supported: true,
            retryInterval: 600,
            testRequestBody: "",
            overFrequencyAutoDisable: true,
            model_mapping: "",
            base64_image_prefix_mapping: "",
            request_token_limit_enabled: process.env.REQUEST_TOKEN_LIMIT_ENABLED === 'true',
            original_model_pricing: false,
            usage_recalculation_enabled: false,
            empty_response_error_enabled: false,
            extra_headers: "",
            excluded_fields: "",
            excluded_response_fields: "",
            extra_fields: "",
            filter_system_prompt: false,
            custom_system_prompt: "",
            arrange_messages: false,
            parse_url_to_content: false,
            parse_url_prefix_enabled: false,
            custom_full_url_enabled: false,
            negative_optimization_enabled: false,
            original_model_fake_resp_enabled: false,
            exclude_custom_prompt_cost_enabled: false,
            force_chat_url_enabled: false,
            claude_stream_enabled: false,
            transparent_proxy_enabled: false,
            ignore_fc_tc_enabled: false,
            filter_stream_ad: false,
            filter_non_stream_ad: false,
            image_in_markdown: false,
            channel_timeout_breaker_time: 0,
            force_o1_stream_enabled: false,
            openai_organization: "",
            min_request_token_count: parseInt(process.env.CHANNEL_MIN_REQUEST_TOKEN_COUNT || '1', 10),
            max_request_token_count: parseInt(process.env.CHANNEL_MAX_REQUEST_TOKEN_COUNT || '16384', 10),
            keyword_error: ""
        }
    }
};
