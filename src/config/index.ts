import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import defaultConfig from './default';
import {loadEasyConfig} from '../utils/easyConfig';

// 尝试从多个可能的位置加载环境变量
const envPaths = [
    path.resolve(process.cwd(), '.env'),
    path.resolve(__dirname, '../../.env'),
    path.resolve(__dirname, '../.env'),
    path.resolve(__dirname, '.env')
];

let envLoaded = false;
for (const envPath of envPaths) {
    if (fs.existsSync(envPath)) {
        console.log(`[配置] 从路径加载环境变量: ${envPath}`);
        dotenv.config({path: envPath});
        envLoaded = true;
        break;
    }
}

if (!envLoaded) {
    console.warn('[配置] 警告: 未找到 .env 文件。确保通过环境变量提供配置。');
}

// 尝试从 EMAIL_EASY_CONFIG 环境变量加载配置
const easyConfigLoaded = loadEasyConfig();
if (easyConfigLoaded) {
    console.log('[配置] 使用 EMAIL_EASY_CONFIG 环境变量中的账号信息');
}

/**
 * Configuration system
 * Loads default config and overrides with environment variables
 */
class Config {
    private config: Record<string, any>;

    constructor() {
        this.config = defaultConfig;
        this.validateRequiredConfig();
    }

    /**
     * Validate that required configuration values are present
     */
    private validateRequiredConfig(): void {
        // 基本配置验证
        const requiredConfig: string[] = [
            'emailProvider.type',
            'registration.mode'
        ];

        // 根据注册模式验证必要的配置
        const registrationMode = this.get('registration.mode');
        if (registrationMode === 'batch') {
            requiredConfig.push(
                'registration.credentialsFile',
                'registration.outputFile',
                'registration.maxConcurrent'
            );
        } else if (registrationMode === 'single') {
            requiredConfig.push(
                'emailProvider.email',
                'emailProvider.password'
            );
        }

        // 跳过开发环境的验证
        if (process.env.NODE_ENV === 'development') {
            return;
        }

        for (const key of requiredConfig) {
            if (!this.get(key)) {
                throw new Error(`缺少必要的配置项: ${key}`);
            }
        }
    }

    /**
     * 将环境变量值转换为适当的类型
     */
    private convertValue(value: string): any {
        // 尝试转换为数字
        if (!isNaN(Number(value))) {
            return Number(value);
        }
        // 尝试转换为布尔值
        if (value.toLowerCase() === 'true') return true;
        if (value.toLowerCase() === 'false') return false;
        // 保持字符串类型
        return value;
    }

    /**
     * Get a configuration value
     * @param key Dot notation path to the config value
     * @param defaultValue Default value if the config value is not found
     */
    get(key: string, defaultValue?: any): any {
        const keys = key.split('.');

        // 首先，尝试直接从环境变量获取
        const envKey = keys.join('_').toUpperCase();
        if (process.env[envKey]) {
            return this.convertValue(process.env[envKey]!);
        }

        // 对于特定的键，直接从环境变量获取
        const envMappings: Record<string, string> = {
            'emailProvider.type': 'EMAIL_PROVIDER_TYPE',
            'emailProvider.email': 'EMAIL_ADDRESS',
            'emailProvider.password': 'EMAIL_PASSWORD',
            'emailProvider.clientId': 'CLIENT_ID',
            'emailProvider.clientSecret': 'CLIENT_SECRET',
            'emailProvider.refreshToken': 'REFRESH_TOKEN',
            'emailProvider.host': 'EMAIL_HOST',
            'emailProvider.port': 'EMAIL_PORT',
            'emailProvider.protocol': 'EMAIL_PROTOCOL',
            'emailProvider.domain': 'DOMAIN',
            'emailProvider.tempMail': 'TEMP_MAIL',
            'emailProvider.tempMailExt': 'TEMP_MAIL_EXT',
            'emailProvider.epin': 'TEMP_MAIL_EPIN',
            'emailProvider.aliasRandomLength': 'MICROSOFT_ALIAS_RANDOM_LENGTH',
            'registration.mode': 'REGISTRATION_MODE',
            'registration.credentialsFile': 'REGISTRATION_CREDENTIALS_FILE',
            'registration.outputFile': 'REGISTRATION_OUTPUT_FILE',
            'registration.maxConcurrent': 'REGISTRATION_MAX_CONCURRENT',
            'registration.delayBetweenRegistrations': 'DELAY_BETWEEN_REGISTRATIONS',
            'registration.batchCount': 'BATCH_REGISTRATION_COUNT',
            // 添加代理相关的环境变量映射
            'browser.proxy.enabled': 'PROXY_ENABLED',
            'browser.proxy.server': 'PROXY_SERVER',
            'browser.proxy.username': 'PROXY_USERNAME',
            'browser.proxy.password': 'PROXY_PASSWORD',
            'browser.proxy.type': 'PROXY_TYPE',
            // 添加流量优化的环境变量映射
            'browser.optimizeBandwidth': 'OPTIMIZE_BANDWIDTH',
            // 添加系统提交相关的环境变量映射
            'systemSubmission.url': 'SYSTEM_SUBMISSION_URL',
            'systemSubmission.authorization': 'SYSTEM_SUBMISSION_AUTHORIZATION',
            'systemSubmission.headers.User-Agent': 'SYSTEM_SUBMISSION_USER_AGENT',
            'systemSubmission.headers.Content-Type': 'SYSTEM_SUBMISSION_CONTENT_TYPE',
            'systemSubmission.headers.Accept': 'SYSTEM_SUBMISSION_ACCEPT',
            'systemSubmission.channelConfig.name': 'CHANNEL_NAME',
            'systemSubmission.channelConfig.type': 'CHANNEL_TYPE',
            'systemSubmission.channelConfig.billing_type': 'CHANNEL_BILLING_TYPE',
            'systemSubmission.channelConfig.group': 'CHANNEL_GROUP',
            'systemSubmission.channelConfig.models': 'CHANNEL_MODELS',
            'systemSubmission.channelConfig.channel_group_id': 'CHANNEL_GROUP_ID',
            'systemSubmission.channelConfig.min_request_token_count': 'CHANNEL_MIN_REQUEST_TOKEN_COUNT',
            'systemSubmission.channelConfig.max_request_token_count': 'CHANNEL_MAX_REQUEST_TOKEN_COUNT',
            'systemSubmission.channelConfig.request_token_limit_enabled': 'REQUEST_TOKEN_LIMIT_ENABLED'
        };

        if (envMappings[key] && process.env[envMappings[key]]) {
            return this.convertValue(process.env[envMappings[key]]!);
        }

        // 否则，从配置对象中获取
        let current: any = this.config;
        for (const k of keys) {
            if (current[k] === undefined) {
                return defaultValue;
            }
            current = current[k];
        }

        // 处理系统提交相关的配置
        if (key.startsWith('systemSubmission.') && !current) {
            return defaultValue;
        }

        return current;
    }

    /**
     * Set a configuration value
     * @param key Dot notation path to the config value
     * @param value New value
     */
    set(key: string, value: any): void {
        const keys = key.split('.');
        let current: any = this.config;

        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!current[k]) {
                current[k] = {};
            }
            current = current[k];
        }

        current[keys[keys.length - 1]] = value;
    }

    /**
     * Get the entire configuration
     */
    getAll(): Record<string, any> {
        return this.config;
    }
}

// Export singleton instance
export default new Config();

