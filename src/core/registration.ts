import { logger } from '../utils/logger';
import config from '../config';
import { PuppeteerBrowser, OpenAIRegistrationData } from '../providers/browser/puppeteer';
import { BaseEmailProvider } from '../providers/email/base';
import { MicrosoftEmailProvider } from '../providers/email/microsoft';
import { GmailEmailProvider } from '../providers/email/gmail';
import { CustomEmailProvider } from '../providers/email/custom';
import fs from 'fs';
import path from 'path';
import { generateRandomString } from '../utils/random';

/**
 * Registration result interface
 */
export interface RegistrationResult {
    success: boolean;
    email?: string;
    apiKey?: string;
    error?: string;
}

/**
 * Email provider type
 */
export enum EmailProviderType {
    MICROSOFT = 'microsoft',
    GMAIL = 'gmail',
    CUSTOM = 'custom',
    MICROSOFT_ALIAS = 'microsoft-alias',
    GMAIL_ALIAS = 'gmail-alias'
}

/**
 * Email credentials for registration
 */
export interface EmailConfig {
    type: EmailProviderType;
    email: string;
    password?: string;
    clientId?: string;
    clientSecret?: string;
    refreshToken?: string;
    host?: string;
    port?: number;
    protocol?: string;
    domain?: string;
    tempMail?: string;
    tempMailExt?: string;
    epin?: string;
    aliasEmail?: string;
}

/**
 * User data for registration
 */
export interface UserData {
    firstName: string;
    lastName: string;
    dateOfBirth: {
        day: string;
        month: string;
        year: string;
    };
    country: string;
    phoneNumber?: string;
}

/**
 * OpenAI account registration manager
 */
export class OpenAIRegistration {
    private browser: PuppeteerBrowser;
    private emailProvider: BaseEmailProvider | null = null;

    constructor() {
        this.browser = new PuppeteerBrowser();
    }

    /**
     * Create appropriate email provider based on type
     */
    private createEmailProvider(emailConfig: EmailConfig): BaseEmailProvider {
        switch (emailConfig.type) {
            case EmailProviderType.MICROSOFT:
                if (!emailConfig.clientId || !emailConfig.refreshToken) {
                    throw new Error('Microsoft email provider requires clientId and refreshToken');
                }
                return new MicrosoftEmailProvider({
                    email: emailConfig.email,
                    clientId: emailConfig.clientId,
                    refreshToken: emailConfig.refreshToken
                });

            case EmailProviderType.MICROSOFT_ALIAS:
                if (!emailConfig.clientId || !emailConfig.refreshToken) {
                    throw new Error('Microsoft Alias email provider requires clientId and refreshToken');
                }
                return new MicrosoftEmailProvider({
                    email: emailConfig.email,
                    password: emailConfig.password,
                    clientId: emailConfig.clientId,
                    refreshToken: emailConfig.refreshToken,
                    useAlias: true,
                    aliasEmail: emailConfig.aliasEmail
                });

            case EmailProviderType.GMAIL:
                if (!emailConfig.clientId || !emailConfig.clientSecret || !emailConfig.refreshToken) {
                    throw new Error('Gmail provider requires clientId, clientSecret, and refreshToken');
                }
                return new GmailEmailProvider({
                    email: emailConfig.email,
                    clientId: emailConfig.clientId,
                    clientSecret: emailConfig.clientSecret,
                    refreshToken: emailConfig.refreshToken
                });

            case EmailProviderType.CUSTOM:
                if (!emailConfig.host || !emailConfig.port || !emailConfig.password) {
                    throw new Error('Custom email provider requires host, port, and password');
                }
                return new CustomEmailProvider({
                    email: emailConfig.email,
                    password: emailConfig.password,
                    host: emailConfig.host,
                    port: emailConfig.port,
                    domain: emailConfig.domain,
                    tempMail: emailConfig.tempMail,
                    tempMailExt: emailConfig.tempMailExt,
                    epin: emailConfig.epin
                });

            case EmailProviderType.GMAIL_ALIAS:
                // if (!emailConfig.host || !emailConfig.port || !emailConfig.password) {
                //     throw new Error('Custom email provider requires host, port, and password');
                // }
                emailConfig.host = 'mail.gamil.com'
                emailConfig.port = 993
                emailConfig.domain = 'gmail.com'

                return new CustomEmailProvider({
                    email: emailConfig.email,
                    password: emailConfig.password,
                    host: emailConfig.host,
                    port: emailConfig.port,
                    domain: emailConfig.domain,
                    tempMail: emailConfig.tempMail,
                    tempMailExt: emailConfig.tempMailExt,
                    epin: emailConfig.epin
                });


            default:
                throw new Error(`Unsupported email provider type: ${emailConfig.type}`);
        }
    }

    /**
     * Register a new OpenAI account
     */
    async register(
        emailConfig: EmailConfig,
        userData: UserData,
        password: string
    ): Promise<RegistrationResult> {
        try {
            // 如果是微软邮箱别名类型，先生成别名邮箱
            if (emailConfig.type === EmailProviderType.MICROSOFT_ALIAS) {
                const emailParts = emailConfig.email.split('@');
                if (emailParts.length !== 2 || !['hotmail.com', 'outlook.com', 'live.com'].includes(emailParts[1].toLowerCase())) {
                    return {
                        success: false,
                        error: '微软邮箱别名只支持 hotmail.com、outlook.com 或 live.com 域名'
                    };
                }

                const username = emailParts[0];
                const domain = emailParts[1];
                // 获取配置的别名前缀或使用默认前缀
                const aliasPrefix = config.get('emailProvider.aliasPrefix') || '';
                // 获取随机字符串长度
                const randomLength = config.get('emailProvider.aliasRandomLength') || 8;
                // 生成随机字符串
                const randomString = generateRandomString(randomLength);
                // 创建最终别名
                const aliasEmail = `${username}+${aliasPrefix}${randomString}@${domain}`;

                logger.info(`使用微软邮箱别名: ${aliasEmail}`);
                // 保存别名到配置
                emailConfig.aliasEmail = aliasEmail;
            }

            // 如果是gmail邮箱别名类型，先生成别名邮箱
            if (emailConfig.type === EmailProviderType.GMAIL_ALIAS) {
                const emailParts = emailConfig.email.split('@');
                if (emailParts.length !== 2) {
                    return {
                        success: false,
                        error: 'gmail邮箱别名格式错误'
                    };
                }

                const username = emailParts[0];
                const domain = emailParts[1];

                // 获取随机字符串长度
                const randomLength = config.get('emailProvider.aliasRandomLength') || 2;

                // 将用户名转为字符数组
                const usernameChars = username.split('');

                // 随机选择要插入点号的位置
                const insertPositions: number[] = []; // 明确指定为数字数组
                const maxDots = Math.min(randomLength, username.length - 1); // 最多插入的点号数量

                for (let i = 0; i < maxDots; i++) {
                    // 随机选择一个位置 (不包括开头位置0)
                    let position: number;
                    do {
                        position = Math.floor(Math.random() * (username.length - 1)) + 1;
                    } while (insertPositions.includes(position));

                    insertPositions.push(position);
                }

                // 对位置进行排序，以便从后向前插入点号(避免位置偏移)
                insertPositions.sort((a, b) => b - a);

                // 从后向前在选定位置插入点号
                for (const position of insertPositions) {
                    usernameChars.splice(position, 0, '.');
                }

                // 重新组合用户名
                const aliasUsername = usernameChars.join('');
                // 创建最终别名
                const aliasEmail = `${aliasUsername}@${domain}`;

                logger.info(`使用gmail邮箱点号别名: ${aliasEmail}`);
                // 保存别名到配置
                emailConfig.aliasEmail = aliasEmail;
            }

            // 决定用于注册的邮箱地址
            const registrationEmail = (emailConfig.type === EmailProviderType.MICROSOFT_ALIAS || emailConfig.type === EmailProviderType.GMAIL_ALIAS )
                ? emailConfig.aliasEmail!
                : emailConfig.email;

            logger.info(`Starting OpenAI registration for ${registrationEmail}`);

            // Create email provider
            this.emailProvider = this.createEmailProvider(emailConfig);

            // Authenticate with email provider
            logger.info('Authenticating with email provider');
            const authenticated = await this.emailProvider.authenticate();
            if (!authenticated) {
                return {
                    success: false,
                    error: `Failed to authenticate with ${emailConfig.type} email provider`
                };
            }

            // Initialize browser
            logger.info('Initializing browser');
            const browserInitialized = await this.browser.initialize(config.get('browser.headless'));
            if (!browserInitialized) {
                return {
                    success: false,
                    error: 'Failed to initialize browser'
                };
            }

            // Navigate to registration page
            logger.info('Navigating to OpenAI registration page');
            const navigated = await this.browser.navigateToRegistration();
            if (!navigated) {
                return {
                    success: false,
                    error: 'Failed to navigate to registration page'
                };
            }

            // Take screenshot for debugging
            if (config.get('debug.saveScreenshots', false) && config.get('browser.screenshotsDir')) {
                const screenshotsDir = config.get('browser.screenshotsDir');
                if (!fs.existsSync(screenshotsDir)) {
                    fs.mkdirSync(screenshotsDir, { recursive: true });
                }
                await this.browser.takeScreenshot(path.join(screenshotsDir, `registration_start_${Date.now()}.png`));
            }

            // Fill registration form
            logger.info('Filling registration form');
            const registrationData: OpenAIRegistrationData = {
                email: registrationEmail,
                password,
                firstName: userData.firstName,
                lastName: userData.lastName,
                dateOfBirth: userData.dateOfBirth,
                country: userData.country,
                phoneNumber: userData.phoneNumber
            };

            // Step 1: 填写基础注册表单（邮箱和密码）
            const formFilled = await this.browser.fillRegistrationForm(registrationData);
            if (!formFilled) {
                return {
                    success: false,
                    error: 'Failed to fill registration form'
                };
            }

            // Take screenshot after form submission
            if (config.get('debug.saveScreenshots', false) && config.get('browser.screenshotsDir')) {
                await this.browser.takeScreenshot(path.join(
                    config.get('browser.screenshotsDir'),
                    `after_form_${Date.now()}.png`
                ));
            }

            // Step 2: 等待验证邮件并获取验证码
            logger.info('Waiting for verification email');
            const fromTime = new Date();
            const verificationResult = await this.emailProvider.getVerificationCode(
                fromTime,
                config.get('registration.maxVerificationAttempts'),
                config.get('registration.delayBetweenAttempts')
            );

            if (!verificationResult.success || !verificationResult.code) {
                return {
                    success: false,
                    error: verificationResult.error || 'Failed to get verification code'
                };
            }

            // Step 3: 输入验证码
            logger.info(`Entering verification code: ${verificationResult.code}`);
            const verificationEntered = await this.browser.enterVerificationCode(verificationResult.code);
            if (!verificationEntered) {
                return {
                    success: false,
                    error: 'Failed to enter verification code'
                };
            }

            // Take screenshot after verification
            if (config.get('debug.saveScreenshots', false) && config.get('browser.screenshotsDir')) {
                await this.browser.takeScreenshot(path.join(
                    config.get('browser.screenshotsDir'),
                    `after_verification_${Date.now()}.png`
                ));
            }

            // Step 4: 填写个人信息表单
            logger.info('Filling personal information form');
            const personalInfoFilled = await this.browser.fillPersonalInfoForm(registrationData);
            if (!personalInfoFilled) {
                return {
                    success: false,
                    error: 'Failed to fill personal information form'
                };
            }

            // Extract API key
            logger.info('Extracting API key');
            const apiKey = await this.browser.extractAPIKey();
            if (!apiKey) {
                return {
                    success: false,
                    error: 'Failed to extract API key'
                };
            }
            logger.info(`Extracted API key: ${apiKey}`);
            console.log(`API Key: ${apiKey}`);
            // Save API key if configured
            if (config.get('output.saveKeysToFile')) {
                this.saveApiKey(registrationEmail, apiKey);
            }

            // 发送 API key 到系统
            const url = config.get('systemSubmission.url');
            const authorization = config.get('systemSubmission.authorization');

            if (url && authorization) {
                try {
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Authorization': authorization,
                            'User-Agent': config.get('systemSubmission.headers.User-Agent'),
                            'Content-Type': config.get('systemSubmission.headers.Content-Type'),
                            'Accept': config.get('systemSubmission.headers.Accept')
                        },
                        body: JSON.stringify({
                            name: emailConfig.aliasEmail || emailConfig.email,
                            remark: config.get('systemSubmission.channelConfig.name'),
                            type: config.get('systemSubmission.channelConfig.type'),
                            billing_type: config.get('systemSubmission.channelConfig.billing_type'),
                            group: config.get('systemSubmission.channelConfig.group'),
                            models: config.get('systemSubmission.channelConfig.models'),
                            base_url: "",
                            config: "{}",
                            channel_group_id: config.get('systemSubmission.channelConfig.channel_group_id'),
                            sort: 0,
                            weight: 0,
                            function_call_enabled: true,
                            image_supported: true,
                            retryInterval: 600,
                            testRequestBody: "",
                            overFrequencyAutoDisable: true,
                            model_mapping: "",
                            base64_image_prefix_mapping: "",
                            request_token_limit_enabled: config.get('systemSubmission.channelConfig.request_token_limit_enabled', false),
                            original_model_pricing: false,
                            usage_recalculation_enabled: false,
                            empty_response_error_enabled: false,
                            extra_headers: "",
                            excluded_fields: "",
                            excluded_response_fields: "",
                            extra_fields: "",
                            filter_system_prompt: false,
                            custom_system_prompt: "",
                            arrange_messages: false,
                            parse_url_to_content: false,
                            parse_url_prefix_enabled: false,
                            custom_full_url_enabled: false,
                            negative_optimization_enabled: false,
                            original_model_fake_resp_enabled: false,
                            exclude_custom_prompt_cost_enabled: false,
                            force_chat_url_enabled: false,
                            claude_stream_enabled: false,
                            transparent_proxy_enabled: false,
                            ignore_fc_tc_enabled: false,
                            filter_stream_ad: false,
                            filter_non_stream_ad: false,
                            image_in_markdown: false,
                            channel_timeout_breaker_time: 0,
                            force_o1_stream_enabled: false,
                            openai_organization: "",
                            min_request_token_count: config.get('systemSubmission.channelConfig.min_request_token_count', 1),
                            max_request_token_count: config.get('systemSubmission.channelConfig.max_request_token_count', 16384),
                            keyword_error: "",
                            key: apiKey
                        })
                    });

                    if (!response.ok) {
                        logger.error(`Failed to send API key to system: ${response.statusText}`);
                        console.error(response.statusText);
                    } else {
                        logger.info('Successfully sent API key to system');
                        console.log(response);
                    }
                } catch (error) {
                    logger.error('Error sending API key to system:', error);
                }
            } else {
                logger.info('System submission configuration not found, skipping API key submission');
            }

            logger.info(`Successfully registered OpenAI account for ${registrationEmail}`);

            return {
                success: true,
                email: registrationEmail,
                apiKey
            };
        } catch (error) {
            logger.error('Registration error:', error);

            // Take error screenshot
            if (config.get('debug.saveScreenshots', false) && config.get('browser.screenshotsDir')) {
                await this.browser.takeScreenshot(path.join(
                    config.get('browser.screenshotsDir'),
                    `error_${Date.now()}.png`
                ));
            }

            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        } finally {
            // Close browser
            await this.browser.close();
        }
    }

    /**
     * Save API key to file
     */
    private saveApiKey(email: string, apiKey: string): void {
        try {
            const keysFile = config.get('output.keysFile');
            let keys: Record<string, any> = {};

            // Read existing keys if file exists
            if (fs.existsSync(keysFile)) {
                const fileContent = fs.readFileSync(keysFile, 'utf-8');
                keys = JSON.parse(fileContent);
            }

            // Add new key
            keys[email] = {
                apiKey,
                createdAt: new Date().toISOString()
            };

            // Write to file
            fs.writeFileSync(keysFile, JSON.stringify(keys, null, 2));
            logger.info(`API key for ${email} saved to ${keysFile}`);
        } catch (error) {
            logger.error('Failed to save API key:', error);
        }
    }
}
