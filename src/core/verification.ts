import { logger } from '../utils/logger';
import { BaseEmailProvider } from '../providers/email/base';
import { MicrosoftEmailProvider } from '../providers/email/microsoft';
import { GmailEmailProvider } from '../providers/email/gmail';
import { CustomEmailProvider } from '../providers/email/custom';
import { EmailConfig, EmailProviderType } from './registration';

/**
 * Verification result
 */
export interface VerificationResult {
    success: boolean;
    code?: string;
    error?: string;
}

/**
 * Email verification manager
 * Standalone utility to get verification codes from emails
 */
export class EmailVerification {
    /**
     * Create appropriate email provider based on config
     */
    private static createEmailProvider(emailConfig: EmailConfig): BaseEmailProvider {
        switch (emailConfig.type) {
            case EmailProviderType.MICROSOFT:
                if (!emailConfig.clientId || !emailConfig.refreshToken) {
                    throw new Error('Microsoft email provider requires clientId and refreshToken');
                }
                return new MicrosoftEmailProvider({
                    email: emailConfig.email,
                    clientId: emailConfig.clientId,
                    refreshToken: emailConfig.refreshToken
                });

            case EmailProviderType.GMAIL:
                if (!emailConfig.clientId || !emailConfig.clientSecret || !emailConfig.refreshToken) {
                    throw new Error('Gmail provider requires clientId, clientSecret, and refreshToken');
                }
                return new GmailEmailProvider({
                    email: emailConfig.email,
                    clientId: emailConfig.clientId,
                    clientSecret: emailConfig.clientSecret,
                    refreshToken: emailConfig.refreshToken
                });

            case EmailProviderType.CUSTOM:
                if (!emailConfig.host || !emailConfig.port || !emailConfig.password) {
                    throw new Error('Custom email provider requires host, port, and password');
                }
                return new CustomEmailProvider({
                    email: emailConfig.email,
                    password: emailConfig.password,
                    host: emailConfig.host,
                    port: emailConfig.port
                });

            default:
                throw new Error(`Unsupported email provider type: ${emailConfig.type}`);
        }
    }

    /**
     * Get verification code from email
     * This is a standalone utility that can be used separately from the registration process
     */
    static async getVerificationCode(
        emailConfig: EmailConfig,
        fromTime: Date = new Date(Date.now() - 10 * 60 * 1000), // Last 10 minutes by default
        maxAttempts: number = 5,
        delayBetweenAttempts: number = 10000
    ): Promise<VerificationResult> {
        let emailProvider: BaseEmailProvider | null = null;

        try {
            // Create email provider
            emailProvider = this.createEmailProvider(emailConfig);

            // Authenticate with email provider
            logger.info(`Authenticating with ${emailConfig.type} email provider for ${emailConfig.email}`);
            const authenticated = await emailProvider.authenticate();
            if (!authenticated) {
                return {
                    success: false,
                    error: `Failed to authenticate with ${emailConfig.type} email provider`
                };
            }

            // Get verification code
            logger.info(`Getting verification code for ${emailConfig.email}`);
            const verificationResult = await emailProvider.getVerificationCode(
                fromTime,
                maxAttempts,
                delayBetweenAttempts
            );

            return verificationResult;
        } catch (error) {
            logger.error('Error getting verification code:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
}
