import {logger} from './utils/logger';
import {EmailConfig, EmailProviderType, OpenAIRegistration, RegistrationResult, UserData} from './core/registration';
import {EmailVerification} from './core/verification';
import {generatePassword, generateUserData, sleep} from './utils/helpers';
import {EmailGenerator} from './utils/emailGenerator';
import config from './config';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import readline from 'readline';
import {loadEasyConfig} from './utils/easyConfig';

// 加载环境变量（确保能在任何目录执行时找到.env文件）
const envPaths = [
    path.resolve(process.cwd(), '.env'),
    path.resolve(__dirname, '../.env'),
    path.resolve(__dirname, '.env')
];

let envLoaded = false;
for (const envPath of envPaths) {
    if (fs.existsSync(envPath)) {
        console.log(`Loading environment variables from: ${envPath}`);
        dotenv.config({path: envPath});
        envLoaded = true;
        break;
    }
}

if (!envLoaded) {
    console.warn('Warning: No .env file found. Make sure to provide configuration through environment variables.');
}

// 尝试从 EMAIL_EASY_CONFIG 环境变量加载配置
const easyConfigLoaded = loadEasyConfig();
if (easyConfigLoaded) {
    console.log('Using account information from EMAIL_EASY_CONFIG environment variable');
}

/**
 * OpenAI-RegFlow: Automated OpenAI account registration system
 * Main entry point
 */
export async function registerAccount(
    emailConfig?: EmailConfig,
    userData?: UserData,
    password?: string
): Promise<void> {
    let finalEmailConfig: EmailConfig;
    let finalUserData: UserData;
    let finalPassword: string;

    // If no email config provided, create one from environment variables
    if (!emailConfig) {
        const emailProviderType = config.get('emailProvider.type').toUpperCase();
        const domain = config.get('emailProvider.domain', '');

        // 如果是自建邮箱，使用EmailGenerator生成邮箱
        if (emailProviderType === 'CUSTOM' && domain) {
            const emailGenerator = new EmailGenerator(domain);
            const accountInfo = emailGenerator.getAccountInfo();

            finalEmailConfig = {
                type: EmailProviderType[emailProviderType as keyof typeof EmailProviderType] || EmailProviderType.CUSTOM,
                email: accountInfo.email,
                password: accountInfo.password,
                host: config.get('emailProvider.host', ''),
                port: config.get('emailProvider.port', undefined),
                protocol: config.get('emailProvider.protocol', 'IMAP'),
                domain: domain,
                tempMail: config.get('emailProvider.tempMail', ''),
                tempMailExt: config.get('emailProvider.tempMailExt', ''),
                epin: config.get('emailProvider.epin', '')
            };

            // 使用生成的用户信息
            finalUserData = {
                firstName: accountInfo.firstName,
                lastName: accountInfo.lastName,
                country: config.get('registration.defaultCountry', 'US'),
                dateOfBirth: {
                    day: '01',
                    month: '01',
                    year: '1990'
                }
            };
            finalPassword = accountInfo.password;
        } else {
            finalEmailConfig = {
                type: emailProviderType === 'custom' ? EmailProviderType.CUSTOM :
                    emailProviderType === 'microsoft' ? EmailProviderType.MICROSOFT :
                        emailProviderType === 'gmail' ? EmailProviderType.GMAIL :
                            EmailProviderType.MICROSOFT,
                email: config.get('emailProvider.email', ''),
                password: config.get('emailProvider.password', ''),
                clientId: config.get('emailProvider.clientId', ''),
                clientSecret: config.get('emailProvider.clientSecret', ''),
                refreshToken: config.get('emailProvider.refreshToken', ''),
                host: config.get('emailProvider.host', ''),
                port: config.get('emailProvider.port', undefined)
            };
            finalUserData = userData || {
                firstName: config.get('user.firstName', 'John'),
                lastName: config.get('user.lastName', 'Doe'),
                country: config.get('registration.defaultCountry', 'US'),
                dateOfBirth: {
                    day: '01',
                    month: '01',
                    year: '1990'
                }
            };
            finalPassword = password || generatePassword();
        }
    } else {
        finalEmailConfig = emailConfig;
        finalUserData = userData || {
            firstName: config.get('user.firstName', 'John'),
            lastName: config.get('user.lastName', 'Doe'),
            country: config.get('registration.defaultCountry', 'US'),
            dateOfBirth: {
                day: '01',
                month: '01',
                year: '1990'
            }
        };
        finalPassword = password || generatePassword();
    }

    // Check if email configuration is valid
    validateEmailConfig(finalEmailConfig);

    logger.info(`Starting registration for ${finalEmailConfig.email} using ${finalEmailConfig.type} provider`);
    console.log(`\n===== Registration Configuration =====`);
    console.log(`Email Provider: ${finalEmailConfig.type}`);
    console.log(`Email Address: ${finalEmailConfig.email}`);
    console.log(`User: ${finalUserData.firstName} ${finalUserData.lastName} (${finalUserData.country})`);
    console.log(`=====================================\n`);

    const registration = new OpenAIRegistration();
    const result = await registration.register(finalEmailConfig, finalUserData, finalPassword);

    if (result.success) {
        logger.info(`✅ Registration successful for ${result.email}`);
        logger.info(`API Key: ${result.apiKey}`);

        // Log the result
        console.log('\n=== Registration Result ===');
        console.log(`Email: ${result.email}`);
        console.log(`API Key: ${result.apiKey}`);
        console.log('===========================\n');
    } else {
        logger.error(`❌ Registration failed: ${result.error}`);
        console.error(`Registration failed: ${result.error}`);
    }
}

/**
 * Validate email configuration and provide helpful error messages
 */
function validateEmailConfig(emailConfig: EmailConfig): void {
    switch (emailConfig.type) {
        case EmailProviderType.MICROSOFT:
            if (!emailConfig.email || !emailConfig.clientId || !emailConfig.refreshToken) {
                let missingFields = [];
                if (!emailConfig.email) missingFields.push('EMAIL_ADDRESS');
                if (!emailConfig.clientId) missingFields.push('CLIENT_ID');
                if (!emailConfig.refreshToken) missingFields.push('REFRESH_TOKEN');

                throw new Error(
                    `Missing required Microsoft email configuration: ${missingFields.join(', ')}. ` +
                    `Please check your .env file and make sure EMAIL_PROVIDER_TYPE=microsoft and the above fields are set.`
                );
            }
            break;

        case EmailProviderType.GMAIL:
            if (!emailConfig.email || !emailConfig.clientId || !emailConfig.clientSecret || !emailConfig.refreshToken) {
                let missingFields = [];
                if (!emailConfig.email) missingFields.push('EMAIL_ADDRESS');
                if (!emailConfig.clientId) missingFields.push('CLIENT_ID');
                if (!emailConfig.clientSecret) missingFields.push('CLIENT_SECRET');
                if (!emailConfig.refreshToken) missingFields.push('REFRESH_TOKEN');

                throw new Error(
                    `Missing required Gmail configuration: ${missingFields.join(', ')}. ` +
                    `Please check your .env file and make sure EMAIL_PROVIDER_TYPE=gmail and the above fields are set.`
                );
            }
            break;

        case EmailProviderType.CUSTOM:
            if (!emailConfig.email || !emailConfig.password || !emailConfig.host || !emailConfig.port) {
                let missingFields = [];
                if (!emailConfig.email) missingFields.push('EMAIL_ADDRESS');
                if (!emailConfig.password) missingFields.push('EMAIL_PASSWORD');
                if (!emailConfig.host) missingFields.push('EMAIL_HOST');
                if (!emailConfig.port) missingFields.push('EMAIL_PORT');

                throw new Error(
                    `Missing required custom email configuration: ${missingFields.join(', ')}. ` +
                    `Please check your .env file and make sure EMAIL_PROVIDER_TYPE=custom and the above fields are set.`
                );
            }
            break;

        default:
            throw new Error(
                `Unknown email provider type: ${emailConfig.type}. ` +
                `Please set EMAIL_PROVIDER_TYPE in your .env file to one of: microsoft, gmail, custom.`
            );
    }
}

/**
 * Process multiple registrations from a config file
 */
export async function processMultipleRegistrations(configFilePath: string): Promise<void> {
    try {
        if (!fs.existsSync(configFilePath)) {
            logger.error(`Config file not found: ${configFilePath}`);
            return;
        }

        const configContent = fs.readFileSync(configFilePath, 'utf-8');
        const registrationConfigs = JSON.parse(configContent);

        logger.info(`Found ${registrationConfigs.length} registrations to process`);

        for (let i = 0; i < registrationConfigs.length; i++) {
            const config = registrationConfigs[i];
            logger.info(`Processing registration ${i + 1}/${registrationConfigs.length}`);

            await registerAccount(
                config.emailConfig,
                config.userData,
                config.password
            );

            // Add delay between registrations to avoid rate limiting
            if (i < registrationConfigs.length - 1) {
                const delayTime = parseInt(config.get('registration.delayBetweenRegistrations', '60000'), 10);
                logger.info(`Waiting ${delayTime / 1000} seconds before next registration`);
                await sleep(delayTime);
            }
        }
    } catch (error) {
        logger.error('Error processing multiple registrations:', error);
    }
}

/**
 * Just get a verification code (standalone utility)
 */
export async function getVerificationCode(emailConfig: EmailConfig): Promise<void> {
    try {
        // Validate email configuration
        validateEmailConfig(emailConfig);

        logger.info(`Getting verification code for ${emailConfig.email} using ${emailConfig.type} provider`);
        console.log(`\n===== Verification Configuration =====`);
        console.log(`Email Provider: ${emailConfig.type}`);
        console.log(`Email Address: ${emailConfig.email}`);
        console.log(`=======================================\n`);

        const result = await EmailVerification.getVerificationCode(emailConfig);

        if (result.success) {
            logger.info(`✅ Verification code: ${result.code}`);
            console.log(`\nVerification code: ${result.code}\n`);
        } else {
            logger.error(`❌ Failed to get verification code: ${result.error}`);
            console.error(`Failed to get verification code: ${result.error}`);
        }
    } catch (error) {
        logger.error('Error getting verification code:', error);
        console.error('Error getting verification code:', error);
    }
}

/**
 * Command line interface
 */
async function main() {
    try {
        // 打印当前环境变量和配置信息
        console.log('\n===== Configuration Debug Info =====');
        console.log('NODE_ENV:', process.env.NODE_ENV);
        console.log('EMAIL_PROVIDER_TYPE:', process.env.EMAIL_PROVIDER_TYPE);
        console.log('EMAIL_ADDRESS:', process.env.EMAIL_ADDRESS);
        console.log('Config values:');
        console.log('- emailProvider.type:', config.get('emailProvider.type'));
        console.log('- emailProvider.email:', config.get('emailProvider.email'));
        console.log('==================================\n');

        // 通过配置决定注册模式，而不是通过命令行参数
        const registrationMode = config.get('registration.mode', 'single');
        logger.info(`注册模式: ${registrationMode}`);

        // 根据注册模式执行不同的逻辑
        if (registrationMode === 'batch') {
            const emailProviderType = config.get('emailProvider.type').toUpperCase();

            if (emailProviderType === 'CUSTOM') {
                // 自建邮箱批量注册
                const customConfig: CustomEmailBatchConfig = {
                    domain: config.get('emailProvider.domain', ''),
                    host: config.get('emailProvider.host', ''),
                    port: parseInt(config.get('emailProvider.port', '993'), 10),
                    protocol: config.get('emailProvider.protocol', 'IMAP'),
                    count: parseInt(config.get('registration.batchCount', '1'), 10),
                    tempMail: config.get('emailProvider.tempMail', ''),
                    tempMailExt: config.get('emailProvider.tempMailExt', ''),
                    epin: config.get('emailProvider.epin', '')
                };

                const outputFile = config.get('registration.outputFile', 'api_keys.txt');
                await batchRegisterCustomEmails(customConfig, outputFile);
            } else if (emailProviderType === 'MICROSOFT-ALIAS') {
                // 微软邮箱别名批量注册
                const msAliasConfig: MicrosoftAliasBatchConfig = {
                    email: config.get('emailProvider.email', ''),
                    password: config.get('emailProvider.password', ''),
                    clientId: config.get('emailProvider.clientId', ''),
                    refreshToken: config.get('emailProvider.refreshToken', ''),
                    count: parseInt(config.get('registration.batchCount', '1'), 10),
                    aliasPrefix: config.get('emailProvider.aliasPrefix', ''),
                    aliasRandomLength: parseInt(config.get('emailProvider.aliasRandomLength', '8'), 10)
                };

                const outputFile = config.get('registration.outputFile', 'api_keys.txt');
                logger.info(`开始微软邮箱别名批量注册，主邮箱: ${msAliasConfig.email}, 注册数量: ${msAliasConfig.count}`);
                await batchRegisterMicrosoftAliasEmails(msAliasConfig, outputFile);
            } else if (emailProviderType === 'GMAIL-ALIAS') {
                // 微软邮箱别名批量注册
                const gmailAliasConfig: GmailAliasBatchConfig = {
                    email: config.get('emailProvider.email', ''),
                    password: config.get('emailProvider.password', ''),
                    clientId: config.get('emailProvider.clientId', ''),
                    refreshToken: config.get('emailProvider.refreshToken', ''),
                    count: parseInt(config.get('registration.batchCount', '1'), 10),
                    aliasPrefix: config.get('emailProvider.aliasPrefix', ''),
                    aliasRandomLength: parseInt(config.get('emailProvider.aliasRandomLength', '8'), 10),
                    tempMail: config.get('emailProvider.tempMail', ''),
                    tempMailExt: config.get('emailProvider.tempMailExt', ''),
                };

                const outputFile = config.get('registration.outputFile', 'api_keys.txt');
                logger.info(`开始微软邮箱别名批量注册，主邮箱: ${gmailAliasConfig.email}, 注册数量: ${gmailAliasConfig.count}`);
                await batchRegisterGmailAliasEmails(gmailAliasConfig, outputFile);
            } else {
                // 原有的Microsoft批量注册
                const credentialsFile = config.get('registration.credentialsFile', 'accounts.txt');
                const outputFile = config.get('registration.outputFile', 'api_keys.txt');
                const maxConcurrent = parseInt(config.get('registration.maxConcurrent', '1'), 10);
                await batchRegister(credentialsFile, outputFile, maxConcurrent);
            }
        } else {
            // 单个注册模式
            const emailProviderType = config.get('emailProvider.type').toLowerCase();
            const domain = config.get('emailProvider.domain', '');

            let emailConfig: EmailConfig;
            let userData: UserData;
            let password: string;

            // 如果是自建邮箱，使用EmailGenerator生成邮箱
            if (emailProviderType === 'custom' && domain) {
                const emailGenerator = new EmailGenerator(domain);
                const accountInfo = emailGenerator.getAccountInfo();

                emailConfig = {
                    type: EmailProviderType.CUSTOM,
                    email: accountInfo.email,
                    password: accountInfo.password,
                    host: config.get('emailProvider.host', ''),
                    port: config.get('emailProvider.port', undefined),
                    protocol: config.get('emailProvider.protocol', 'IMAP'),
                    domain: domain,
                    tempMail: config.get('emailProvider.tempMail', ''),
                    tempMailExt: config.get('emailProvider.tempMailExt', ''),
                    epin: config.get('emailProvider.epin', '')
                };

                // 使用生成的用户信息
                userData = {
                    firstName: accountInfo.firstName,
                    lastName: accountInfo.lastName,
                    country: config.get('registration.defaultCountry', 'US'),
                    dateOfBirth: {
                        day: '01',
                        month: '01',
                        year: '1990'
                    }
                };
                password = accountInfo.password;
            } else {
                // 其他邮箱类型（Microsoft或Gmail）
                emailConfig = {
                    type: emailProviderType === 'microsoft' ? EmailProviderType.MICROSOFT :
                        emailProviderType === 'gmail' ? EmailProviderType.GMAIL :
                            EmailProviderType.MICROSOFT,
                    email: config.get('emailProvider.email', ''),
                    password: config.get('emailProvider.password', ''),
                    clientId: config.get('emailProvider.clientId', ''),
                    clientSecret: config.get('emailProvider.clientSecret', ''),
                    refreshToken: config.get('emailProvider.refreshToken', ''),
                    host: config.get('emailProvider.host', ''),
                    port: config.get('emailProvider.port', undefined)
                };

                userData = {
                    firstName: config.get('user.firstName', 'John'),
                    lastName: config.get('user.lastName', 'Doe'),
                    country: config.get('registration.defaultCountry', 'US'),
                    dateOfBirth: {
                        day: '01',
                        month: '01',
                        year: '1990'
                    }
                };
                password = config.get('emailProvider.password', '');
            }

            logger.info(`使用单个账号注册模式，邮箱提供商: ${emailConfig.type}`);
            await registerAccount(emailConfig, userData, password);
        }

        // 还保留命令行功能，用于特殊用途
        const args = process.argv.slice(2);
        const command = args[0];

        if (command) {
            switch (command) {
                case 'verify':
                    // 仅获取验证码
                    const verifyEmailProviderType = config.get('emailProvider.type').toUpperCase();

                    const verifyEmailConfig: EmailConfig = {
                        type: verifyEmailProviderType === 'custom' ? EmailProviderType.CUSTOM :
                            verifyEmailProviderType === 'microsoft' ? EmailProviderType.MICROSOFT :
                                verifyEmailProviderType === 'gmail' ? EmailProviderType.GMAIL :
                                    EmailProviderType.MICROSOFT,
                        email: config.get('emailProvider.email', ''),
                        password: config.get('emailProvider.password', ''),
                        clientId: config.get('emailProvider.clientId', ''),
                        clientSecret: config.get('emailProvider.clientSecret', ''),
                        refreshToken: config.get('emailProvider.refreshToken', ''),
                        host: config.get('emailProvider.host', ''),
                        port: config.get('emailProvider.port', undefined)
                    };

                    await getVerificationCode(verifyEmailConfig);
                    break;

                case 'batch':
                    // 命令行参数指定的批量注册
                    let credentialsFile = 'accounts.txt';
                    let outputFile = 'api_keys.txt';
                    let maxConcurrent = 1;

                    // 解析批量注册相关的参数
                    for (let i = 1; i < args.length; i++) {
                        if (args[i] === '--credentials' && i + 1 < args.length) {
                            credentialsFile = args[i + 1];
                            i++;
                        } else if (args[i] === '--output' && i + 1 < args.length) {
                            outputFile = args[i + 1];
                            i++;
                        } else if (args[i] === '--concurrent' && i + 1 < args.length) {
                            maxConcurrent = parseInt(args[i + 1], 10);
                            if (isNaN(maxConcurrent) || maxConcurrent < 1) {
                                maxConcurrent = 1;
                            }
                            i++;
                        }
                    }

                    logger.info(`通过命令行执行批量注册`);
                    logger.info(`凭证文件: ${credentialsFile}`);
                    logger.info(`输出文件: ${outputFile}`);
                    logger.info(`最大并发数: ${maxConcurrent}`);

                    await batchRegister(credentialsFile, outputFile, maxConcurrent);
                    break;

                case 'multi':
                    // 从JSON配置文件处理多个注册
                    if (args.length < 2) {
                        console.error('错误: 缺少配置文件。用法: node index.js multi <config-file.json>');
                        process.exit(1);
                    }
                    await processMultipleRegistrations(args[1]);
                    break;

                case 'help':
                    console.log(`
OpenAI-RegFlow - 自动化OpenAI账号注册工具

用法:
  npm start                       使用配置文件中的设置进行注册
  npm start -- [command] [options]  使用命令行参数

命令:
  verify                获取邮箱验证码(不注册)
  multi <config.json>   从JSON配置文件处理多个注册
  batch [options]       从凭证文件批量注册账号
  help                  显示帮助信息

批量模式选项:
  --credentials <file>  凭证文件 (默认: accounts.txt)
  --output <file>       输出API密钥文件 (默认: api_keys.txt)
  --concurrent <num>    最大并发注册数 (默认: 1)

配置文件设置:
  可以在.env文件中设置以下变量来配置注册模式:
  
  # 注册模式设置
  REGISTRATION_MODE=single         # 单个账号注册模式
  REGISTRATION_MODE=batch          # 批量注册模式
  REGISTRATION_CREDENTIALS_FILE=accounts.txt   # 批量模式的凭证文件
  REGISTRATION_OUTPUT_FILE=api_keys.txt       # 批量模式的输出文件
  REGISTRATION_MAX_CONCURRENT=2              # 批量模式的最大并发数
                    `);
                    break;

                default:
                    // 不处理，已经通过配置运行了默认行为
                    break;
            }
        }
    } catch (error) {
        logger.error('主程序错误:', error);
        console.error('错误:', error);
        process.exit(1);
    }
}

// Run main function if this file is executed directly
if (require.main === module) {
    main().catch(error => {
        logger.error('Unhandled error:', error);
        console.error('Unhandled error:', error);
        process.exit(1);
    });
}

// Export functions for programmatic use
export {OpenAIRegistration, EmailVerification, EmailProviderType};

/**
 * 账号凭证数据结构
 */
interface AccountCredential {
    email: string;
    password: string;
    refreshToken: string;
    clientId: string;
}

/**
 * 从文件中读取账号凭证
 * 格式: email----password----refreshToken----clientId
 */
async function readCredentialsFromFile(filePath: string): Promise<AccountCredential[]> {
    const credentials: AccountCredential[] = [];

    try {
        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            throw new Error(`文件不存在: ${filePath}`);
        }

        // 创建文件读取流
        const fileStream = fs.createReadStream(filePath);
        const rl = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity
        });

        // 逐行读取
        for await (const line of rl) {
            // 跳过空行
            if (!line.trim()) continue;

            // 解析格式: email----password----refreshToken----clientId
            const parts = line.split('----');
            if (parts.length >= 4) {
                credentials.push({
                    email: parts[0].trim(),
                    password: parts[1].trim(),
                    refreshToken: parts[2].trim(),
                    clientId: parts[3].trim()
                });
            } else {
                logger.warn(`无效的凭证格式: ${line}`);
            }
        }

        logger.info(`从文件中读取了 ${credentials.length} 个账号凭证`);
        return credentials;
    } catch (error) {
        logger.error(`读取凭证文件失败:`, error);
        throw error;
    }
}

/**
 * 保存API密钥到文件
 */
function saveApiKeys(keys: {
    email: string,
    apiKey: string
}[], filePath: string, isCustomEmail: boolean = false): void {
    try {
        const content = keys.map(k => {
            // 如果是自建邮箱，只输出 API 密钥
            if (isCustomEmail) {
                return k.apiKey;
            }
            // 其他类型维持原有格式
            return `${k.email}----${k.apiKey}`;
        }).join('\n');

        // 确保目录存在
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, {recursive: true});
        }

        // 写入文件
        fs.writeFileSync(filePath, content);
        logger.info(`API密钥已保存到: ${filePath}`);
    } catch (error) {
        logger.error(`保存API密钥失败:`, error);
    }
}

/**
 * 处理单个账号注册
 */
async function registerSingleAccount(credential: AccountCredential): Promise<RegistrationResult> {
    const registration = new OpenAIRegistration();

    try {
        logger.info(`开始为 ${credential.email} 注册OpenAI账号`);
        let emailProviderType = EmailProviderType.MICROSOFT;
        if (process.env.EMAIL_PROVIDER_TYPE === EmailProviderType.GMAIL_ALIAS) {
            emailProviderType = EmailProviderType.GMAIL_ALIAS
        } else if (process.env.EMAIL_PROVIDER_TYPE === EmailProviderType.MICROSOFT_ALIAS) {
            emailProviderType = EmailProviderType.MICROSOFT_ALIAS
        } else if (process.env.EMAIL_PROVIDER_TYPE === EmailProviderType.GMAIL) {
            emailProviderType = EmailProviderType.GMAIL
        } else if (process.env.EMAIL_PROVIDER_TYPE === EmailProviderType.CUSTOM) {
            emailProviderType = EmailProviderType.CUSTOM
        }
        const result = await registration.register(
            {
                type: emailProviderType,
                email: credential.email,
                refreshToken: credential.refreshToken,
                clientId: credential.clientId
            },
            {
                firstName: config.get('user.firstName', 'John'),
                lastName: config.get('user.lastName', 'Doe'),
                dateOfBirth: {
                    day: config.get('user.dateOfBirth.day', '01'),
                    month: config.get('user.dateOfBirth.month', '01'),
                    year: config.get('user.dateOfBirth.year', '1990')
                },
                country: config.get('user.country', 'US')
            },
            (process.env.EMAIL_PROVIDER_TYPE == EmailProviderType.MICROSOFT_ALIAS || process.env.EMAIL_PROVIDER_TYPE == EmailProviderType.GMAIL_ALIAS ||
                process.env.EMAIL_PROVIDER_TYPE == EmailProviderType.MICROSOFT) ? generatePassword() : credential.password
        );

        return result;
    } catch (error) {
        logger.error(`注册过程出错: ${error}`);
        return {
            success: false,
            email: credential.email,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * 批量注册主函数
 */
async function batchRegister(credentialsFilePath: string, outputFilePath: string, maxConcurrent: number = 1): Promise<void> {
    try {
        // 读取凭证
        const credentials = await readCredentialsFromFile(credentialsFilePath);
        if (credentials.length === 0) {
            logger.error('没有读取到有效的账号凭证');
            return;
        }

        logger.info(`开始批量注册，共 ${credentials.length} 个账号，最大并发数: ${maxConcurrent}`);

        const results: RegistrationResult[] = [];
        const successfulKeys: { email: string, apiKey: string }[] = [];

        // 使用简单的队列处理方式实现并发控制
        const queue = [...credentials];
        const inProgress: Promise<void>[] = [];

        // 启动初始任务
        const startInitialTasks = Math.min(maxConcurrent, queue.length);
        for (let i = 0; i < startInitialTasks; i++) {
            const credential = queue.shift();
            if (credential) {
                const task = processCredential(credential);
                inProgress.push(task);
            }
        }

        // 等待所有任务完成
        await Promise.all(inProgress);

        // 保存成功获取的API密钥
        if (successfulKeys.length > 0) {
            saveApiKeys(successfulKeys, outputFilePath, false);
        }

        // 输出统计信息
        const successful = results.filter(r => r.success).length;
        logger.info(`批量注册完成: 成功 ${successful}/${credentials.length}`);

        // 内部函数：处理单个凭证并管理队列
        async function processCredential(credential: AccountCredential): Promise<void> {
            try {
                // 注册账号
                const result = await registerSingleAccount(credential);
                results.push(result);

                // 如果成功且有API密钥，添加到结果列表
                if (result.success && result.apiKey) {
                    successfulKeys.push({
                        email: credential.email,
                        apiKey: result.apiKey
                    });

                    // 实时保存到文件，防止中断丢失数据
                    saveApiKeys(successfulKeys, outputFilePath, false);
                }

                // 注册下一个账号
                if (queue.length > 0) {
                    const nextCredential = queue.shift();
                    if (nextCredential) {
                        // 继续处理队列中的下一个
                        const nextTask = processCredential(nextCredential);
                        // 替换当前任务在inProgress数组中的位置
                        const index = inProgress.indexOf(processCredential as unknown as Promise<void>);
                        if (index !== -1) {
                            inProgress[index] = nextTask;
                        }
                    }
                }

                // 账号间延迟，避免被检测
                const delay = config.get('registration.delayBetweenRegistrations', 60000);
                await new Promise(resolve => setTimeout(resolve, delay));
            } catch (error) {
                logger.error(`处理账号 ${credential.email} 失败:`, error);

                // 即使失败也要处理队列中的下一个
                if (queue.length > 0) {
                    const nextCredential = queue.shift();
                    if (nextCredential) {
                        const nextTask = processCredential(nextCredential);
                        const index = inProgress.indexOf(processCredential as unknown as Promise<void>);
                        if (index !== -1) {
                            inProgress[index] = nextTask;
                        }
                    }
                }
            }
        }
    } catch (error) {
        logger.error('批量注册过程出错:', error);
    }
}

// 导出主要功能供其他模块使用
export {
    batchRegister,
    registerSingleAccount,
    readCredentialsFromFile,
    saveApiKeys
};

/**
 * 自建邮箱批量注册配置
 */
interface CustomEmailBatchConfig {
    domain: string;
    host: string;
    port: number;
    protocol: string;
    count: number;
    tempMail: string;
    tempMailExt: string;
    epin: string;
}

/**
 * 批量注册自建邮箱账号
 */
async function batchRegisterCustomEmails(customConfig: CustomEmailBatchConfig, outputFilePath: string): Promise<void> {
    try {
        const registrationCount = parseInt(config.get('registration.batchCount', '10'), 10);
        logger.info(`开始批量注册自建邮箱账号，计划注册数量: ${registrationCount}`);

        const emailGenerator = new EmailGenerator(customConfig.domain);
        const results: RegistrationResult[] = [];
        const successfulKeys: { email: string, apiKey: string }[] = [];

        for (let i = 0; i < registrationCount; i++) {
            try {
                logger.info(`正在注册第 ${i + 1}/${registrationCount} 个账号`);
                const accountInfo = emailGenerator.getAccountInfo();

                const emailConfig: EmailConfig = {
                    type: EmailProviderType.CUSTOM,
                    email: accountInfo.email,
                    password: accountInfo.password,
                    host: customConfig.host,
                    port: customConfig.port,
                    protocol: customConfig.protocol,
                    domain: customConfig.domain,
                    tempMail: customConfig.tempMail,
                    tempMailExt: customConfig.tempMailExt,
                    epin: customConfig.epin
                };

                const userData: UserData = {
                    firstName: accountInfo.firstName,
                    lastName: accountInfo.lastName,
                    country: 'US', // 默认国家
                    dateOfBirth: {
                        day: '01',
                        month: '01',
                        year: '1990'
                    }
                };

                const registration = new OpenAIRegistration();
                const result = await registration.register(emailConfig, userData, accountInfo.password);
                results.push(result);

                if (result.success && result.apiKey) {
                    successfulKeys.push({
                        email: accountInfo.email,
                        apiKey: result.apiKey
                    });

                    // 实时保存到文件，防止中断丢失数据
                    saveApiKeys(successfulKeys, outputFilePath, true);
                }

                // 账号间延迟，避免被检测
                const delay = parseInt(config.get('registration.delayBetweenRegistrations', '60000'), 10);
                logger.info(`等待 ${delay / 1000} 秒后进行下一次注册...`);
                await new Promise(resolve => setTimeout(resolve, delay));

            } catch (error) {
                logger.error(`注册第 ${i + 1} 个账号失败:`, error);
            }
        }

        // 输出统计信息
        const successful = results.filter(r => r.success).length;
        logger.info(`批量注册完成: 成功 ${successful}/${registrationCount}`);

    } catch (error) {
        logger.error('批量注册过程出错:', error);
    }
}

/**
 * 微软邮箱别名批量注册配置
 */
interface MicrosoftAliasBatchConfig {
    email: string;               // 主邮箱地址
    password: string;            // 主邮箱密码
    clientId: string;            // Microsoft OAuth客户端ID
    refreshToken: string;        // 刷新令牌
    count: number;               // 要注册的账号数量
    aliasPrefix: string;         // 别名前缀
    aliasRandomLength: number;   // 随机字符串长度
}

/**
 * 使用微软邮箱别名批量注册OpenAI账号
 * @param config 微软邮箱别名批量注册配置
 * @param outputFile 输出API密钥的文件路径
 */
async function batchRegisterMicrosoftAliasEmails(msAliasConfig: MicrosoftAliasBatchConfig, outputFile: string): Promise<void> {
    if (!msAliasConfig.email || !msAliasConfig.clientId || !msAliasConfig.refreshToken) {
        logger.error('微软邮箱别名批量注册缺少必要参数: 邮箱、客户端ID或刷新令牌');
        return;
    }

    logger.info(`开始使用微软邮箱别名批量注册 ${msAliasConfig.count} 个OpenAI账号`);
    logger.info(`主邮箱: ${msAliasConfig.email}, 别名前缀: ${msAliasConfig.aliasPrefix}`);

    // 按顺序注册账号
    for (let i = 0; i < msAliasConfig.count; i++) {
        try {
            logger.info(`正在处理第 ${i + 1}/${msAliasConfig.count} 个注册`);

            // 创建随机用户信息
            const userData = generateUserData();
            const password = generatePassword();

            // 使用OpenAIRegistration类的注册方法
            const registration = new OpenAIRegistration();

            // 设置邮箱配置
            const emailConfig: EmailConfig = {
                type: EmailProviderType.MICROSOFT_ALIAS,
                email: msAliasConfig.email,
                password: msAliasConfig.password,
                clientId: msAliasConfig.clientId,
                refreshToken: msAliasConfig.refreshToken
            };

            // 执行注册
            const result = await registration.register(emailConfig, userData, password);

            // 处理结果
            if (result.success) {
                logger.info(`第 ${i + 1}/${msAliasConfig.count} 个账号注册成功！`);
                logger.info(`邮箱: ${result.email}`);
                logger.info(`API密钥: ${result.apiKey}`);

                // 保存到文件
                if (result.email && result.apiKey) {
                    const line = `${result.email}----${result.apiKey}\n`;
                    fs.appendFileSync(outputFile, line);
                }
            } else {
                logger.error(`第 ${i + 1}/${msAliasConfig.count} 个账号注册失败: ${result.error}`);
            }

            // 等待一段时间再注册下一个账号
            const delayTime = parseInt(config.get('registration.delayBetweenRegistrations', '60000'), 10);
            if (i < msAliasConfig.count - 1) {
                logger.info(`等待 ${delayTime / 1000} 秒后继续下一个注册...`);
                await new Promise(resolve => setTimeout(resolve, delayTime));
            }
        } catch (error) {
            logger.error(`处理第 ${i + 1}/${msAliasConfig.count} 个注册时出错:`, error);
        }
    }

    logger.info(`微软邮箱别名批量注册完成。结果已保存到 ${outputFile}`);
}


/**
 * GMAIL邮箱别名批量注册配置
 */
interface GmailAliasBatchConfig {
    email: string;               // 主邮箱地址
    password: string;            // 主邮箱密码
    clientId: string;            // Microsoft OAuth客户端ID
    refreshToken: string;        // 刷新令牌
    count: number;               // 要注册的账号数量
    aliasPrefix: string;         // 别名前缀
    aliasRandomLength: number;   // 随机字符串长度
    tempMail: string;           // 临时邮箱
    tempMailExt: string;        // 临时邮箱扩展名
}


/**
 * 使用gmail邮箱别名批量注册OpenAI账号
 * @param config gmail邮箱别名批量注册配置
 * @param outputFile 输出API密钥的文件路径
 */
async function batchRegisterGmailAliasEmails(gmailAliasConfig: GmailAliasBatchConfig, outputFile: string): Promise<void> {
    if (!gmailAliasConfig.email || !gmailAliasConfig.clientId || !gmailAliasConfig.refreshToken) {
        logger.error('微软邮箱别名批量注册缺少必要参数: 邮箱、客户端ID或刷新令牌');
        return;
    }

    logger.info(`开始使用微软邮箱别名批量注册 ${gmailAliasConfig.count} 个OpenAI账号`);
    logger.info(`主邮箱: ${gmailAliasConfig.email}, 别名前缀: ${gmailAliasConfig.aliasPrefix}`);

    // 按顺序注册账号
    for (let i = 0; i < gmailAliasConfig.count; i++) {
        try {
            logger.info(`正在处理第 ${i + 1}/${gmailAliasConfig.count} 个注册`);

            // 创建随机用户信息
            const userData = generateUserData();
            const password = generatePassword();

            // 使用OpenAIRegistration类的注册方法
            const registration = new OpenAIRegistration();

            // 设置邮箱配置
            const emailConfig: EmailConfig = {
                type: EmailProviderType.GMAIL_ALIAS,
                email: gmailAliasConfig.email,
                password: gmailAliasConfig.password,
                clientId: gmailAliasConfig.clientId,
                refreshToken: gmailAliasConfig.refreshToken,
                tempMail: gmailAliasConfig.tempMail,
                tempMailExt: gmailAliasConfig.tempMailExt
            };

            // 执行注册
            const result = await registration.register(emailConfig, userData, password);

            // 处理结果
            if (result.success) {
                logger.info(`第 ${i + 1}/${gmailAliasConfig.count} 个账号注册成功！`);
                logger.info(`邮箱: ${result.email}`);
                logger.info(`API密钥: ${result.apiKey}`);

                // 保存到文件
                if (result.email && result.apiKey) {
                    const line = `${result.email}----${result.apiKey}\n`;
                    fs.appendFileSync(outputFile, line);
                }
            } else {
                logger.error(`第 ${i + 1}/${gmailAliasConfig.count} 个账号注册失败: ${result.error}`);
            }

            // 等待一段时间再注册下一个账号
            const delayTime = parseInt(config.get('registration.delayBetweenRegistrations', '60000'), 10);
            if (i < gmailAliasConfig.count - 1) {
                logger.info(`等待 ${delayTime / 1000} 秒后继续下一个注册...`);
                await new Promise(resolve => setTimeout(resolve, delayTime));
            }
        } catch (error) {
            logger.error(`处理第 ${i + 1}/${gmailAliasConfig.count} 个注册时出错:`, error);
        }
    }

    logger.info(`微软邮箱别名批量注册完成。结果已保存到 ${outputFile}`);
}
