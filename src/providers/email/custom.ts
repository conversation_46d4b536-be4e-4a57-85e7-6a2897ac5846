import { BaseEmailProvider, EmailCredentials, VerificationResult } from './base';
import { logger } from '../../utils/logger';
import Imap from 'imap';
import * as fs from 'fs';
import * as stream from 'stream';
import * as net from 'net';
import * as tls from 'tls';

/**
 * Custom/Self-hosted email provider implementation
 * Supports both IMAP and POP3 protocols
 */
export class CustomEmailProvider extends BaseEmailProvider {
    private imap: Imap | null = null;
    private protocol: 'IMAP' | 'POP3';
    private is163Mail: boolean = false;
    private domain: string = '';

    constructor(credentials: EmailCredentials) {
        super(credentials);
        if (!credentials.host || !credentials.port || !credentials.email || !credentials.password) {
            throw new Error('Custom email provider requires host, port, email, and password');
        }
        this.protocol = (credentials.protocol || 'IMAP').toUpperCase() as 'IMAP' | 'POP3';
        this.is163Mail = credentials.email.endsWith('@163.com') ||
                        credentials.email.endsWith('@126.com') ||
                        credentials.email.endsWith('@yeah.net');
        this.domain = credentials.domain || '';
    }

    /**
     * Authenticate with the email server
     */
    async authenticate(): Promise<boolean> {
        try {
            // 如果是自建邮箱，直接返回true，不需要认证
            logger.info(`Checking domain: ${this.domain}`);
            if (this.domain) {
                logger.info('使用自建邮箱，跳过认证');
                return true;
            }

            // 原有的IMAP/POP3认证逻辑
            if (this.credentials.protocol === 'POP3') {
                return await this.authenticatePop3();
            } else {
                return await this.authenticateImap();
            }
        } catch (error) {
            logger.error('Custom email authentication error:', error);
            return false;
        }
    }

    /**
     * Authenticate with IMAP server
     */
    private async authenticateImap(): Promise<boolean> {
        return new Promise((resolve) => {
            try {
                const { host, port, email, password } = this.credentials;

                if (!host || !port || !email || !password) {
                    throw new Error('Missing required credentials: host, port, email, or password');
                }

                this.imap = new Imap({
                    user: email,
                    password,
                    host,
                    port,
                    tls: true,
                    tlsOptions: { rejectUnauthorized: false } // For servers with self-signed certificates
                });

                const imapInstance = this.imap;

                imapInstance.once('ready', () => {
                    logger.info('Successfully authenticated with IMAP server');
                    resolve(true);
                });

                imapInstance.once('error', (err: Error) => {
                    logger.error('IMAP authentication error:', err);
                    this.imap = null;
                    resolve(false);
                });

                imapInstance.connect();
            } catch (error) {
                logger.error('Error during IMAP authentication:', error);
                resolve(false);
            }
        });
    }

    /**
     * Authenticate with POP3 server
     */
    private async authenticatePop3(): Promise<boolean> {
        return new Promise((resolve) => {
            try {
                const { host, port, email, password } = this.credentials;
                if (!host || !port) {
                    throw new Error('Host and port are required for POP3 authentication');
                }
                const socket = net.connect({ host, port });
                const tlsSocket = tls.connect({
                    socket: socket,
                    rejectUnauthorized: false
                });

                tlsSocket.on('secureConnect', () => {
                    // Send USER command
                    tlsSocket.write(`USER ${email}\r\n`);
                });

                tlsSocket.on('data', (data) => {
                    const response = data.toString();
                    if (response.startsWith('+OK')) {
                        if (response.includes('USER')) {
                            // Send PASS command
                            tlsSocket.write(`PASS ${password}\r\n`);
                        } else if (response.includes('PASS')) {
                            logger.info('Successfully authenticated with POP3 server');
                            tlsSocket.end();
                            resolve(true);
                        }
                    } else {
                        logger.error('POP3 authentication error:', response);
                        tlsSocket.end();
                        resolve(false);
                    }
                });

                tlsSocket.on('error', (err) => {
                    logger.error('POP3 connection error:', err);
                    resolve(false);
                });
            } catch (error) {
                logger.error('Error during POP3 authentication:', error);
                resolve(false);
            }
        });
    }

    /**
     * Fetch emails using POP3
     */
    private async fetchEmailsPop3(fromTime: Date): Promise<string[]> {
        return new Promise((resolve, reject) => {
            try {
                const { host, port, email, password } = this.credentials;
                if (!host || !port) {
                    throw new Error('Host and port are required for POP3 email fetching');
                }
                const socket = net.connect({ host, port });
                const tlsSocket = tls.connect({
                    socket: socket,
                    rejectUnauthorized: false
                });

                const emails: string[] = [];
                let currentEmail = '';

                tlsSocket.on('secureConnect', () => {
                    tlsSocket.write(`USER ${email}\r\n`);
                });

                tlsSocket.on('data', (data) => {
                    const response = data.toString();
                    if (response.startsWith('+OK')) {
                        if (response.includes('USER')) {
                            tlsSocket.write(`PASS ${password}\r\n`);
                        } else if (response.includes('PASS')) {
                            tlsSocket.write('LIST\r\n');
                        } else if (response.includes('LIST')) {
                            // Get the last 10 messages
                            const lines = response.split('\r\n');
                            const messageCount = parseInt(lines[1].split(' ')[0]);
                            const start = Math.max(1, messageCount - 9);

                            for (let i = start; i <= messageCount; i++) {
                                tlsSocket.write(`RETR ${i}\r\n`);
                            }
                        } else if (response.includes('RETR')) {
                            currentEmail += response;
                            if (response.includes('\r\n.\r\n')) {
                                emails.push(currentEmail);
                                currentEmail = '';
                            }
                        }
                    }
                });

                tlsSocket.on('end', () => {
                    resolve(emails);
                });

                tlsSocket.on('error', (err) => {
                    reject(err);
                });
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Extract verification code from email content
     */
    private extractVerificationCode(emailContent: string): string | null {
        try {
            // Pattern 1: 6-digit code not preceded by letters or domain-related symbols
            const codeMatch = emailContent.match(/(?<![a-zA-Z@.])\b\d{6}\b/);
            if (codeMatch) {
                return codeMatch[0];
            }

            // Pattern 2: Code in HTML format
            const htmlCodeMatch = emailContent.match(/<td[^>]*>\s*(\d{6})\s*<\/td>/i);
            if (htmlCodeMatch && htmlCodeMatch[1]) {
                return htmlCodeMatch[1];
            }

            // Pattern 3: Code after common keywords
            const keywordMatch = emailContent.match(/(?:code|verification code|verify your email)[^0-9]*(\d{6})/i);
            if (keywordMatch && keywordMatch[1]) {
                return keywordMatch[1];
            }

            return null;
        } catch (error) {
            logger.error('Error extracting verification code:', error);
            return null;
        }
    }

    /**
     * Parse raw email content without using mailparser
     */
    private parseEmail(rawEmail: string): {html?: string, text?: string} {
        const result: {html?: string, text?: string} = {};

        // Find text/plain part
        const textMatch = rawEmail.match(/Content-Type: text\/plain(?:[\s\S]*?)(?:\r?\n\r?\n)([\s\S]*?)(?:\r?\n\r?\n--)/i);
        if (textMatch && textMatch[1]) {
            result.text = textMatch[1].trim();
        }

        // Find text/html part
        const htmlMatch = rawEmail.match(/Content-Type: text\/html(?:[\s\S]*?)(?:\r?\n\r?\n)([\s\S]*?)(?:\r?\n\r?\n--)/i);
        if (htmlMatch && htmlMatch[1]) {
            result.html = htmlMatch[1].trim();
        }

        return result;
    }

    /**
     * Fetch recent emails from the email server
     */
    private async fetchEmails(fromTime: Date): Promise<string[]> {
        if (this.protocol === 'IMAP') {
            return this.fetchEmailsImap(fromTime);
        } else {
            return this.fetchEmailsPop3(fromTime);
        }
    }

    /**
     * Fetch emails using IMAP
     */
    private async fetchEmailsImap(fromTime: Date): Promise<string[]> {
        return new Promise((resolve, reject) => {
            if (!this.imap) {
                reject(new Error('Not authenticated. Call authenticate() first'));
                return;
            }

            const emails: string[] = [];
            const imapInstance = this.imap;

            // Format date for IMAP search
            const searchDate = fromTime.toISOString().substring(0, 10).replace(/-/g, '');

            const search = [
                ['SINCE', searchDate],
                ['FROM', 'openai.com']
            ];

            imapInstance.openBox('INBOX', true, (err: Error | null, box) => {
                if (err) {
                    reject(err);
                    return;
                }

                imapInstance.search(search, (err: Error | null, results: number[]) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    if (!results || results.length === 0) {
                        resolve([]);
                        return;
                    }

                    const f = imapInstance.fetch(results, { bodies: '' });

                    f.on('message', (msg) => {
                        msg.on('body', (stream: NodeJS.ReadableStream) => {
                            let buffer = '';

                            stream.on('data', (chunk: Buffer) => {
                                buffer += chunk.toString('utf8');
                            });

                            stream.once('end', () => {
                                // Parse email without mailparser
                                const parsedEmail = this.parseEmail(buffer);
                                let content = '';

                                if (parsedEmail.html) {
                                    content += parsedEmail.html;
                                }

                                if (parsedEmail.text) {
                                    content += parsedEmail.text;
                                }

                                if (!content && buffer) {
                                    // Fallback to using the raw email
                                    content = buffer;
                                }

                                emails.push(content);
                            });
                        });
                    });

                    f.once('error', (err: Error) => {
                        reject(err);
                    });

                    f.once('end', () => {
                        resolve(emails);
                    });
                });
            });
        });
    }

    /**
     * Get verification code from custom email server
     */
    private async getTempMailVerificationCode(
        fromTime: Date,
        maxAttempts: number = 10,
        delayBetweenAttempts: number = 10000
    ): Promise<{ success: boolean; code?: string; error?: string }> {
        let attempts = 0;
        while (attempts < maxAttempts) {
            try {
                logger.info(`Attempt ${attempts + 1} to get verification code from temp mail`);

                // 获取邮件列表
                const mailListUrl = `https://tempmail.plus/api/mails?email=${this.credentials.tempMail}${this.credentials.tempMailExt}&limit=20&epin=${this.credentials.epin}`;
                const mailListResponse = await fetch(mailListUrl);
                const mailListData = await mailListResponse.json();

                if (!mailListData.result) {
                    logger.info('No mails found in temp mail');
                    attempts++;
                    if (attempts < maxAttempts) {
                        logger.info(`Waiting ${delayBetweenAttempts / 1000} seconds before next attempt`);
                        await this.wait(delayBetweenAttempts);
                    }
                    continue;
                }

                // 获取最新邮件的ID
                const firstId = mailListData.first_id;
                if (!firstId) {
                    logger.info('No mail ID found');
                    attempts++;
                    if (attempts < maxAttempts) {
                        logger.info(`Waiting ${delayBetweenAttempts / 1000} seconds before next attempt`);
                        await this.wait(delayBetweenAttempts);
                    }
                    continue;
                }

                // 获取具体邮件内容
                const mailDetailUrl = `https://tempmail.plus/api/mails/${firstId}?email=${this.credentials.tempMail}${this.credentials.tempMailExt}&epin=${this.credentials.epin}`;
                const mailDetailResponse = await fetch(mailDetailUrl);
                const mailDetailData = await mailDetailResponse.json();

                if (!mailDetailData.result) {
                    logger.info('Failed to get mail detail');
                    attempts++;
                    if (attempts < maxAttempts) {
                        logger.info(`Waiting ${delayBetweenAttempts / 1000} seconds before next attempt`);
                        await this.wait(delayBetweenAttempts);
                    }
                    continue;
                }

                // 从邮件文本中提取6位数字验证码
                const mailText = mailDetailData.text || '';
                const mailSubject = mailDetailData.subject || '';
                logger.info(`Found mail subject: ${mailSubject}`);

                const codeMatch = mailText.match(/(?<![a-zA-Z@.])\b\d{6}\b/);
                if (codeMatch) {
                    logger.info(`Found verification code: ${codeMatch[0]}`);

                    // 清理邮件
                    await this.cleanupMail(firstId);

                    return { success: true, code: codeMatch[0] };
                }

                logger.info(`No verification code found in attempt ${attempts + 1}`);
                attempts++;
                if (attempts < maxAttempts) {
                    logger.info(`Waiting ${delayBetweenAttempts / 1000} seconds before next attempt`);
                    await this.wait(delayBetweenAttempts);
                }
            } catch (error) {
                logger.error(`Error in attempt ${attempts + 1}:`, error);
                attempts++;
                if (attempts < maxAttempts) {
                    await this.wait(delayBetweenAttempts);
                }
            }
        }

        return { success: false, error: `Failed to find verification code after ${maxAttempts} attempts` };
    }

    private async cleanupMail(firstId: string): Promise<boolean> {
        const deleteUrl = 'https://tempmail.plus/api/mails/';
        const formData = new URLSearchParams();
        formData.append('email', this.credentials.tempMail || '');
        formData.append('first_id', String(Number(firstId))); // 确保是数字类型
        formData.append('epin', this.credentials.tempMailEpin || '');

        // 最多尝试5次
        for (let i = 0; i < 5; i++) {
            try {
                const response = await fetch(deleteUrl, {
                    method: 'DELETE',
                    headers: {
                        'accept': 'application/json, text/javascript, */*; q=0.01',
                        'accept-language': 'zh-CN,zh;q=0.9',
                        'cache-control': 'no-cache',
                        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                        'origin': 'https://tempmail.plus',
                        'pragma': 'no-cache',
                        'priority': 'u=1, i',
                        'referer': 'https://tempmail.plus/zh/',
                        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                        'sec-ch-ua-mobile': '?0',
                        'sec-ch-ua-platform': '"macOS"',
                        'sec-fetch-dest': 'empty',
                        'sec-fetch-mode': 'cors',
                        'sec-fetch-site': 'same-origin',
                        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'x-requested-with': 'XMLHttpRequest'
                    },
                    body: formData.toString()
                });

                const result = await response.json();
                if (result.result === true) {
                    return true;
                }
            } catch (error) {
                logger.error(`Failed to cleanup mail, attempt ${i + 1}:`, error);
            }

            await this.wait(500); // 等待0.5秒
        }

        return false;
    }

    private async getVerificationCodePop3(
        fromTime: Date,
        maxAttempts: number = 10,
        delayBetweenAttempts: number = 10000
    ): Promise<{ success: boolean; code?: string; error?: string }> {
        let attempts = 0;
        while (attempts < maxAttempts) {
            try {
                logger.info(`Attempt ${attempts + 1} to get verification code via POP3`);

                const emails = await this.fetchEmailsPop3(fromTime);
                if (emails.length === 0) {
                    logger.info('No emails found via POP3');
                    attempts++;
                    if (attempts < maxAttempts) {
                        logger.info(`Waiting ${delayBetweenAttempts / 1000} seconds before next attempt`);
                        await this.wait(delayBetweenAttempts);
                    }
                    continue;
                }

                // 从最新的邮件开始检查
                for (const email of emails) {
                    const code = this.extractVerificationCode(email);
                    if (code) {
                        logger.info(`Found verification code via POP3: ${code}`);
                        return { success: true, code };
                    }
                }

                logger.info(`No verification code found in attempt ${attempts + 1} via POP3`);
                attempts++;
                if (attempts < maxAttempts) {
                    logger.info(`Waiting ${delayBetweenAttempts / 1000} seconds before next attempt`);
                    await this.wait(delayBetweenAttempts);
                }
            } catch (error) {
                logger.error(`Error in attempt ${attempts + 1} via POP3:`, error);
                attempts++;
                if (attempts < maxAttempts) {
                    await this.wait(delayBetweenAttempts);
                }
            }
        }

        return { success: false, error: `Failed to find verification code after ${maxAttempts} attempts via POP3` };
    }

    private async getVerificationCodeImap(
        fromTime: Date,
        maxAttempts: number = 10,
        delayBetweenAttempts: number = 10000
    ): Promise<{ success: boolean; code?: string; error?: string }> {
        let attempts = 0;
        while (attempts < maxAttempts) {
            try {
                logger.info(`Attempt ${attempts + 1} to get verification code via IMAP`);

                const emails = await this.fetchEmailsImap(fromTime);
                if (emails.length === 0) {
                    logger.info('No emails found via IMAP');
                    attempts++;
                    if (attempts < maxAttempts) {
                        logger.info(`Waiting ${delayBetweenAttempts / 1000} seconds before next attempt`);
                        await this.wait(delayBetweenAttempts);
                    }
                    continue;
                }

                // 从最新的邮件开始检查
                for (const email of emails) {
                    const code = this.extractVerificationCode(email);
                    if (code) {
                        logger.info(`Found verification code via IMAP: ${code}`);
                        return { success: true, code };
                    }
                }

                logger.info(`No verification code found in attempt ${attempts + 1} via IMAP`);
                attempts++;
                if (attempts < maxAttempts) {
                    logger.info(`Waiting ${delayBetweenAttempts / 1000} seconds before next attempt`);
                    await this.wait(delayBetweenAttempts);
                }
            } catch (error) {
                logger.error(`Error in attempt ${attempts + 1} via IMAP:`, error);
                attempts++;
                if (attempts < maxAttempts) {
                    await this.wait(delayBetweenAttempts);
                }
            }
        }

        return { success: false, error: `Failed to find verification code after ${maxAttempts} attempts via IMAP` };
    }

    async getVerificationCode(
        fromTime: Date,
        maxAttempts: number = 10,
        delayBetweenAttempts: number = 10000
    ): Promise<{ success: boolean; code?: string; error?: string }> {
        try {
            // 如果是自建邮箱，使用临时邮箱服务获取验证码
            if (this.credentials.domain) {
                logger.info('使用临时邮箱服务获取验证码');
                return await this.getTempMailVerificationCode(fromTime, maxAttempts, delayBetweenAttempts);
            }

            // 原有的IMAP/POP3验证码获取逻辑
            if (this.credentials.protocol === 'POP3') {
                return await this.getVerificationCodePop3(fromTime, maxAttempts, delayBetweenAttempts);
            } else {
                return await this.getVerificationCodeImap(fromTime, maxAttempts, delayBetweenAttempts);
            }
        } catch (error) {
            logger.error('Failed to get verification code:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
}
