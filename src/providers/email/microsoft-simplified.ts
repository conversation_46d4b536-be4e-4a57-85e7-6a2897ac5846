import axios from 'axios';
import { BaseEmailProvider, EmailCredentials, VerificationResult } from './base';
import { logger } from '../../utils/logger';
import { Buffer } from 'buffer';
import * as util from 'util';
import * as tls from 'tls';

/**
 * 简化版的Microsoft Outlook/Hotmail邮件提供者
 * 基于测试类的直接实现，减少复杂度
 */
export class MicrosoftEmailProvider extends BaseEmailProvider {
    private accessToken: string | null = null;
    private POP3_SERVER = 'outlook.office365.com';
    private POP3_PORT = 995;

    constructor(credentials: EmailCredentials) {
        super(credentials);
        if (!credentials.clientId || !credentials.refreshToken) {
            throw new Error('Microsoft email provider requires clientId and refreshToken');
        }
    }

    /**
     * 与Microsoft认证获取访问令牌
     */
    async authenticate(): Promise<boolean> {
        try {
            const { clientId, refreshToken } = this.credentials;

            if (!clientId || !refreshToken) {
                throw new Error('Missing required credentials: clientId or refreshToken');
            }

            logger.info('正在使用refresh token获取access token...');

            const data = new URLSearchParams({
                client_id: clientId,
                grant_type: 'refresh_token',
                refresh_token: refreshToken
            });

            const response = await axios.post('https://login.live.com/oauth20_token.srf', data);

            if (response.status !== 200) {
                throw new Error(`Authentication failed with status ${response.status}`);
            }

            this.accessToken = response.data.access_token;
            logger.info('成功获取Microsoft访问令牌');
            return true;
        } catch (error) {
            logger.error('Microsoft认证失败:', error);
            return false;
        }
    }

    /**
     * 生成用于POP3认证的OAuth2字符串
     */
    private generateAuthString(user: string, token: string): string {
        const authString = `user=${user}\u0001auth=Bearer ${token}\u0001\u0001`;
        return Buffer.from(authString).toString('base64');
    }

    /**
     * 简化的POP3连接实现，仅使用一种简单直接的方法
     */
    private async fetchEmails(): Promise<string[]> {
        return new Promise(async (resolve, reject) => {
            try {
                if (!this.accessToken) {
                    logger.error('未获取访问令牌，请先调用authenticate()方法');
                    reject(new Error('未获取访问令牌，请先调用authenticate()方法'));
                    return;
                }

                logger.info('正在连接到微软POP3服务器...');

                // 创建TLS套接字
                const socket = tls.connect({
                    host: this.POP3_SERVER,
                    port: this.POP3_PORT,
                    rejectUnauthorized: false  // 在某些环境可能需要设置为false
                });

                let buffer = '';
                const messages: string[] = [];
                let currentMessage = '';
                let isMultiline = false;

                // 设置超时
                const timeout = setTimeout(() => {
                    logger.error('POP3连接超时');
                    socket.end();
                    reject(new Error('POP3连接超时'));
                }, 60000); // 60秒超时

                // 处理数据
                socket.on('data', (data: Buffer) => {
                    const dataStr = data.toString('utf8');
                    buffer += dataStr;

                    logger.debug(`接收到POP3数据: ${dataStr.substring(0, 100)}...`);

                    if (isMultiline) {
                        // 处理多行响应（邮件内容）
                        if (buffer.includes('\r\n.\r\n')) {
                            const endIndex = buffer.indexOf('\r\n.\r\n');
                            currentMessage += buffer.substring(0, endIndex);
                            messages.push(currentMessage);

                            buffer = buffer.substring(endIndex + 5);
                            currentMessage = '';
                            isMultiline = false;

                            // 如果我们已经获取到了足够的消息，关闭连接
                            if (messages.length >= 5) {
                                socket.write('QUIT\r\n');
                                socket.end();
                                clearTimeout(timeout);
                                resolve(messages);
                            }
                        } else {
                            // 继续接收消息
                            currentMessage += buffer;
                            buffer = '';
                        }
                    } else if (buffer.includes('\r\n')) {
                        // 处理命令响应
                        const lines = buffer.split('\r\n');
                        buffer = lines.pop() || '';

                        for (const line of lines) {
                            logger.debug(`POP3响应: ${line}`);

                            // 处理RETR命令的响应
                            if (line.startsWith('+OK') && line.includes('octets')) {
                                isMultiline = true;  // 切换到多行模式接收邮件内容
                            }
                        }
                    }
                });

                // 错误处理
                socket.on('error', (err: Error) => {
                    logger.error('POP3连接错误:', err);
                    clearTimeout(timeout);
                    reject(err);
                });

                // 连接关闭
                socket.on('close', () => {
                    logger.debug('POP3连接关闭');
                    clearTimeout(timeout);
                    if (messages.length > 0) {
                        resolve(messages);
                    } else {
                        reject(new Error('连接关闭，未获取到任何邮件'));
                    }
                });

                // 连接建立后的处理
                socket.on('connect', async () => {
                    try {
                        logger.info('已连接到POP3服务器');

                        // 等待服务器的欢迎消息
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // 发送认证命令
                        logger.debug('发送认证命令...');
                        socket.write('AUTH XOAUTH2\r\n');
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // 发送认证字符串
                        const authString = this.generateAuthString(this.credentials.email!, this.accessToken!);
                        logger.debug('发送认证字符串...');
                        socket.write(authString + '\r\n');
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // 获取邮箱状态
                        logger.debug('获取邮箱状态...');
                        socket.write('STAT\r\n');
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // 判断STAT响应(假设我们有响应)并获取消息数量
                        let numMessages = 5; // 默认获取5封
                        const statMatch = buffer.match(/\+OK (\d+)/);
                        if (statMatch) {
                            numMessages = parseInt(statMatch[1], 10);
                            logger.info(`邮箱中有 ${numMessages} 封邮件`);
                        } else {
                            logger.warn('无法获取邮件数量，将尝试获取最新的5封邮件');
                        }

                        // 获取最新的几封邮件
                        const startIndex = Math.max(1, numMessages - 4);
                        for (let i = startIndex; i <= numMessages; i++) {
                            logger.debug(`获取第 ${i} 封邮件...`);
                            socket.write(`RETR ${i}\r\n`);
                            await new Promise(resolve => setTimeout(resolve, 2000));
                        }
                    } catch (error) {
                        logger.error('POP3连接处理错误:', error);
                        socket.end();
                        reject(error);
                    }
                });
            } catch (error) {
                logger.error('POP3连接初始化错误:', error);
                reject(error);
            }
        });
    }

    /**
     * 从邮件内容中提取OpenAI验证码
     */
    private extractVerificationCode(emailContent: string): string | null {
        try {
            // 记录邮件摘要用于调试
            const preview = emailContent.length > 100
                ? emailContent.substring(0, 100) + '...'
                : emailContent;
            logger.debug(`处理邮件内容: ${preview}`);

            // 验证码模式匹配 - 按优先级排序
            const patterns = [
                // 最可能的模式 - 常规验证码提示
                /(?:code|verification code|verify your email)[^0-9]*(\d{6})/i,

                // HTML邮件中的验证码
                /<td[^>]*>\s*(\d{6})\s*<\/td>/i,

                // OpenAI特定模式
                /OpenAI[^\d]*(\d{6})/i,

                // 最后的尝试 - 任何6位数字
                /\b(\d{6})\b/
            ];

            // 尝试每种模式
            for (const pattern of patterns) {
                const match = emailContent.match(pattern);
                if (match && match[1]) {
                    logger.info(`找到验证码: ${match[1]}`);
                    return match[1];
                }
            }

            logger.debug('未找到验证码');
            return null;
        } catch (error) {
            logger.error('提取验证码时出错:', error);
            return null;
        }
    }

    /**
     * 获取验证码
     */
    async getVerificationCode(
        fromTime: Date = new Date(Date.now() - 10 * 60 * 1000), // 默认获取最近10分钟的邮件
        maxAttempts: number = 5,
        delayBetweenAttempts: number = 10000
    ): Promise<VerificationResult> {
        let attempts = 0;

        // 确保已认证
        if (!this.accessToken) {
            logger.info('尚未认证，正在获取访问令牌...');
            const authenticated = await this.authenticate();
            if (!authenticated) {
                return { success: false, error: 'Microsoft认证失败' };
            }
        }

        while (attempts < maxAttempts) {
            try {
                logger.info(`尝试获取验证码 (尝试 ${attempts + 1}/${maxAttempts})...`);

                // 获取邮件
                const messages = await this.fetchEmails();
                logger.info(`获取到 ${messages.length} 封邮件`);

                // 检查邮件中的验证码
                for (const message of messages) {
                    const code = this.extractVerificationCode(message);
                    if (code) {
                        logger.info(`成功找到验证码: ${code}`);
                        return { success: true, code };
                    }
                }

                // 没有找到验证码，尝试下一次
                logger.info(`尝试 ${attempts + 1} 未找到验证码`);
                attempts++;

                // 等待下一次尝试
                if (attempts < maxAttempts) {
                    logger.info(`等待 ${delayBetweenAttempts/1000} 秒后再次尝试...`);
                    await this.wait(delayBetweenAttempts);
                }
            } catch (error) {
                logger.error(`尝试 ${attempts + 1} 出错:`, error);
                attempts++;

                // 等待下一次尝试
                if (attempts < maxAttempts) {
                    await this.wait(delayBetweenAttempts);
                }
            }
        }

        return { success: false, error: `在 ${maxAttempts} 次尝试后未找到验证码` };
    }
}
