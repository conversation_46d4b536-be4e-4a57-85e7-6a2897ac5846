import axios from 'axios';
import { BaseEmailProvider, EmailCredentials, VerificationResult } from './base';
import { logger } from '../../utils/logger';
import { Buffer } from 'buffer';
import * as tls from 'tls';
import * as net from 'net';
import { simpleParser } from 'mailparser';
import { EmailHelper } from '../../utils/email-helper';
import Imap from 'imap';
import { inspect } from 'util';

/**
 * Microsoft Outlook/Hotmail email provider implementation
 * Uses OAuth2 for authentication and IMAP for email retrieval
 */
export class MicrosoftEmailProvider extends BaseEmailProvider {
    private accessToken: string | null = null;
    private POP3_SERVER = 'outlook.office365.com';
    private POP3_PORT = 995;
    private IMAP_SERVER = 'outlook.office365.com';
    private IMAP_PORT = 993;
    private useAlias: boolean = false;
    private aliasEmail: string | null = null;
    // 可能的垃圾邮件文件夹名称，将尝试这些名称
    private POSSIBLE_JUNK_FOLDERS = ['Junk Email', 'Junk', 'Spam', '垃圾邮件', '垃圾郵件', 'Courrier indésirable'];

    constructor(credentials: EmailCredentials) {
        super(credentials);
        if (!credentials.clientId || !credentials.refreshToken) {
            throw new Error('Microsoft email provider requires clientId and refreshToken');
        }

        // 设置别名邮箱相关属性
        this.useAlias = credentials.useAlias || false;
        this.aliasEmail = credentials.aliasEmail || null;
    }

    /**
     * Authenticate with Microsoft using refresh token to get access token
     */
    async authenticate(): Promise<boolean> {
        try {
            const { clientId, refreshToken } = this.credentials;

            if (!clientId || !refreshToken) {
                throw new Error('Missing required credentials: clientId or refreshToken');
            }

            const data = new URLSearchParams({
                client_id: clientId,
                grant_type: 'refresh_token',
                refresh_token: refreshToken
            });

            const response = await axios.post('https://login.live.com/oauth20_token.srf', data);

            if (response.status !== 200) {
                throw new Error(`Authentication failed with status ${response.status}`);
            }

            this.accessToken = response.data.access_token;
            logger.info('Successfully authenticated with Microsoft');
            return true;
        } catch (error) {
            logger.error('Microsoft authentication failed:', error);
            return false;
        }
    }

    /**
     * Generate OAuth2 authentication string for IMAP/POP3
     */
    private generateAuthString(user: string, token: string): string {
        const authString = `user=${user}\u0001auth=Bearer ${token}\u0001\u0001`;
        return Buffer.from(authString).toString('base64');
    }

    /**
     * 创建IMAP连接
     * 使用IMAP协议访问微软邮箱，包括垃圾邮件文件夹
     */
    private async createImapConnection(retryCount = 3, retryDelay = 2000): Promise<Imap> {
        let attempt = 0;
        let imap: Imap | null = null;

        while (attempt < retryCount) {
            try {
                if (!this.accessToken) {
                    throw new Error('Not authenticated. Call authenticate() first');
                }

                logger.info(`创建IMAP连接...(尝试 ${attempt + 1}/${retryCount})`);

                // 创建IMAP连接对象
                imap = new Imap({
                    user: this.credentials.email!,
                    password: this.credentials.password || '', // 必须提供，虽然我们使用OAuth2，但类型要求有此字段
                    xoauth2: this.generateAuthString(this.credentials.email!, this.accessToken!),
                    host: this.IMAP_SERVER,
                    port: this.IMAP_PORT,
                    tls: true,
                    tlsOptions: {
                        rejectUnauthorized: true,
                        enableTrace: false,  // 禁用调试跟踪
                        secureProtocol: 'TLSv1_2_method' // 明确指定TLS协议版本
                    },
                    authTimeout: 60000,  // 减少超时，更快失败
                    connTimeout: 60000   // 减少超时，更快失败
                });

                // 使用Promise包装连接过程，添加超时控制
                return await Promise.race([
                    new Promise<Imap>((resolve, reject) => {
                        // 连接错误处理
                        const errorHandler = (err: Error) => {
                            logger.error(`IMAP连接错误 (尝试 ${attempt + 1}/${retryCount}):`, err);
                            reject(err);
                        };

                        // 连接成功处理
                        const readyHandler = () => {
                            logger.info(`IMAP连接就绪 (尝试 ${attempt + 1}/${retryCount})`);
                            // 移除其他事件监听器，防止内存泄漏
                            imap!.removeAllListeners('error');
                            resolve(imap!);
                        };

                        // 为所有可能的错误事件添加监听器
                        imap!.once('error', errorHandler);
                        imap!.once('close', (hadError: boolean) => {
                            if (hadError) {
                                logger.error(`IMAP连接异常关闭 (尝试 ${attempt + 1}/${retryCount})`);
                                reject(new Error('IMAP connection closed with error'));
                            }
                        });
                        imap!.once('end', () => {
                            logger.debug(`IMAP连接结束 (尝试 ${attempt + 1}/${retryCount})`);
                        });

                        // 连接就绪处理
                        imap!.once('ready', readyHandler);

                        // 开始连接
                        imap!.connect();
                    }),
                    // 添加25秒的整体超时控制
                    new Promise<never>((_, reject) => {
                        setTimeout(() => {
                            if (imap) {
                                try {
                                    // 尝试安全关闭
                                    (imap as unknown as { end: () => void }).end();
                                } catch (endError) {
                                    // 忽略关闭错误
                                }
                            }
                            reject(new Error(`IMAP连接超时 (尝试 ${attempt + 1}/${retryCount})`));
                        }, 60000); // 25秒超时，比IMAP库内置超时更短，确保我们先处理
                    })
                ]);
            } catch (error: any) {
                logger.error(`创建IMAP连接失败 (尝试 ${attempt + 1}/${retryCount}):`, error);

                // 安全关闭可能部分创建的连接
                if (imap) {
                    try {
                        (imap as unknown as { end: () => void }).end();
                    } catch (endError) {
                        // 忽略关闭错误
                    }
                    imap = null;
                }

                // 最后一次尝试失败则直接抛出错误
                if (attempt === retryCount - 1) {
                    throw error;
                }

                // 检查是否是连接被断开的错误，这种情况需要重试
                const shouldRetry = error.code === 'EPIPE' ||
                    error.message?.includes('socket has been ended') ||
                    error.message?.includes('connection closed') ||
                    error.message?.includes('timeout');

                if (!shouldRetry) {
                    throw error; // 如果不是连接问题，直接抛出错误
                }

                // 等待一段时间后重试
                logger.info(`等待 ${retryDelay}ms 后重试IMAP连接...`);
                await this.wait(retryDelay);

                // 增加重试延迟（指数退避）
                retryDelay = Math.min(retryDelay * 1.5, 10000);
                attempt++;
            }
        }

        throw new Error(`在 ${retryCount} 次尝试后仍无法创建IMAP连接`);
    }

    /**
     * 使用IMAP获取邮件
     * 从收件箱和垃圾邮件文件夹获取邮件
     * 增加重试机制和错误处理
     */
    private async getMessagesViaIMAP(): Promise<string[]> {
        let imap: Imap | null = null;
        const messages: string[] = [];
        let maxRetries = 2;
        let retryCount = 0;

        // 增加全局超时保护
        const globalTimeout = setTimeout(() => {
            logger.error('IMAP获取邮件全局超时，强制结束操作');
            if (imap) {
                try {
                    (imap as unknown as { end: () => void }).end();
                } catch (e) {
                    // 忽略关闭错误
                }
            }
        }, 3 * 60 * 1000); // 3分钟全局超时，确保不会永久卡住

        try {
            while (retryCount <= maxRetries) {
                try {
                    // 创建IMAP连接
                    imap = await this.createImapConnection();

                    // 为imap连接添加错误事件监听，捕获异步错误
                    const errorHandler = (err: Error) => {
                        logger.error(`IMAP连接异步错误: ${err.message}`);
                        // 不在这里关闭连接，让正常流程处理
                    };
                    imap.on('error', errorHandler);

                    try {
                        // 获取文件夹列表 - 设置超时，防止卡在这里
                        const folderPromise = this.getMailboxFolders(imap);
                        const timeoutPromise = new Promise<string[]>((_, reject) => {
                            setTimeout(() => {
                                reject(new Error('获取文件夹列表操作超时'));
                            }, 60000); // 60秒超时
                        });

                        // 使用Promise.race，哪个先完成就用哪个的结果
                        const folders = await Promise.race([folderPromise, timeoutPromise])
                            .catch(error => {
                                logger.error('获取文件夹列表失败:', error);
                                return [] as string[]; // 出错时返回空数组
                            });

                        logger.info(`获取到的邮箱文件夹列表: ${folders.join(', ')}`);

                        // 如果没有获取到文件夹，但已经过了第一次尝试，就只检查INBOX
                        if (folders.length === 0 && retryCount > 0) {
                            logger.warn('未获取到文件夹列表，仅尝试检查INBOX');
                            try {
                                await this.getMessagesFromImapFolder(imap, 'INBOX', messages);
                            } catch (inboxError) {
                                logger.error('获取INBOX邮件失败:', inboxError);
                            }
                        } else {
                            // 正常处理，先获取收件箱邮件
                            try {
                                await this.getMessagesFromImapFolder(imap, 'INBOX', messages);
                            } catch (inboxError) {
                                logger.error('获取INBOX邮件失败:', inboxError);
                            }

                            // 尝试找到垃圾邮件文件夹并获取邮件
                            if (folders.length > 0) {
                                let junkFolder = this.findJunkFolder(folders);
                                if (junkFolder) {
                                    logger.info(`找到垃圾邮件文件夹: ${junkFolder}`);
                                    try {
                                        await this.getMessagesFromImapFolder(imap, junkFolder, messages);
                                    } catch (junkError) {
                                        logger.error(`获取${junkFolder}文件夹邮件失败:`, junkError);
                                    }
                                } else {
                                    logger.warn('未找到垃圾邮件文件夹，只处理收件箱邮件');
                                }
                            }
                        }

                        // 移除错误监听器
                        imap.removeListener('error', errorHandler);

                        // 成功获取邮件，退出循环
                        break;
                    } catch (operationError) {
                        // 移除错误监听器
                        imap.removeListener('error', errorHandler);

                        // 重新抛出错误，让外层catch处理
                        throw operationError;
                    }
                } catch (error: any) {
                    logger.error(`IMAP获取邮件失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);

                    // 判断是否是连接相关错误
                    const isConnectionError = error.code === 'EPIPE' ||
                        error.message?.includes('socket has been ended') ||
                        error.message?.includes('connection closed') ||
                        error.message?.includes('timeout');

                    // 如果是最后一次尝试或不是连接错误
                    if (retryCount >= maxRetries || !isConnectionError) {
                        logger.warn('放弃获取邮件，返回已获取的消息');
                        break; // 不抛出错误，返回已获取的消息
                    }

                    // 等待后重试
                    const retryDelay = 3000 * (retryCount + 1); // 递增延迟
                    logger.info(`等待 ${retryDelay}ms 后重试获取邮件...`);
                    await this.wait(retryDelay);
                    retryCount++;
                } finally {
                    // 确保关闭连接
                    if (imap) {
                        try {
                            // 使用类型断言规避TypeScript类型检查
                            (imap as unknown as { end: () => void }).end();
                            imap = null; // 重置imap对象以便下次重试创建新连接
                        } catch (endError) {
                            logger.debug('关闭IMAP连接时出错，忽略', endError);
                        }
                    }
                }
            }

            return messages;
        } finally {
            // 清除全局超时
            clearTimeout(globalTimeout);
        }
    }

    /**
     * 获取邮箱文件夹列表
     * @param imap IMAP连接
     * @returns 文件夹名称数组
     */
    private getMailboxFolders(imap: Imap): Promise<string[]> {
        return new Promise((resolve, reject) => {
            logger.info('获取邮箱文件夹列表...');

            // 为文件夹操作添加超时控制
            const timeoutId = setTimeout(() => {
                logger.error('获取邮箱文件夹列表操作超时');
                resolve([]); // 超时时返回空数组，不中断流程
            }, 60000); // 40秒超时

            try {
                // 添加错误事件监听
                const errorHandler = (err: Error) => {
                    clearTimeout(timeoutId);
                    logger.error('获取邮箱文件夹列表时发生IMAP错误:', err);
                    resolve([]); // 出错时返回空数组，不中断流程
                };

                // 为IMAP连接添加一次性错误处理器
                imap.once('error', errorHandler);

                imap.getBoxes((err, boxes) => {
                    // 移除错误处理器
                    imap.removeListener('error', errorHandler);

                    clearTimeout(timeoutId);

                    if (err) {
                        logger.error('获取邮箱文件夹列表失败:', err);
                        resolve([]); // 即使失败也返回空数组，不中断流程
                        return;
                    }

                    // 从嵌套的文件夹结构中提取所有文件夹名称
                    const folderNames: string[] = [];

                    try {
                        this.extractFolderNames(boxes, '', folderNames);
                    } catch (extractError) {
                        logger.error('解析文件夹结构时出错:', extractError);
                        // 即使解析出错，也尝试返回已提取的文件夹
                    }

                    logger.info(`获取到 ${folderNames.length} 个邮箱文件夹`);
                    resolve(folderNames);
                });
            } catch (error) {
                clearTimeout(timeoutId);
                logger.error('调用getBoxes方法时发生异常:', error);
                resolve([]); // 出现异常时返回空数组，不中断流程
            }
        });
    }

    /**
     * 从嵌套的文件夹结构中提取所有文件夹名称
     * @param boxes 文件夹对象
     * @param prefix 当前路径前缀
     * @param result 存储结果的数组
     */
    private extractFolderNames(boxes: any, prefix: string, result: string[]): void {
        Object.keys(boxes).forEach(key => {
            const fullName = prefix ? `${prefix}${key}` : key;
            result.push(fullName);

            // 递归处理子文件夹
            if (boxes[key].children) {
                this.extractFolderNames(boxes[key].children, `${fullName}/`, result);
            }
        });
    }

    /**
     * 查找垃圾邮件文件夹
     * @param folders 可用的文件夹列表
     * @returns 找到的垃圾邮件文件夹名称，如果未找到则返回null
     */
    private findJunkFolder(folders: string[]): string | null {
        // 转换为小写进行比较
        const lowerCaseFolders = folders.map(f => f.toLowerCase());

        // 尝试找到匹配的垃圾邮件文件夹
        for (const junkFolder of this.POSSIBLE_JUNK_FOLDERS) {
            const index = lowerCaseFolders.findIndex(f => f.includes(junkFolder.toLowerCase()));
            if (index !== -1) {
                return folders[index]; // 返回原始大小写的文件夹名
            }
        }

        // 通用匹配，查找包含"junk"或"spam"的文件夹
        const index = lowerCaseFolders.findIndex(f =>
            f.includes('junk') ||
            f.includes('spam') ||
            f.includes('trash') ||
            f.includes('废') ||
            f.includes('垃圾')
        );
        if (index !== -1) {
            return folders[index];
        }

        return null;
    }

    /**
     * 从IMAP指定文件夹获取邮件
     * @param imap IMAP连接
     * @param folderName 文件夹名称
     * @param messages 存储邮件的数组
     */
    private async getMessagesFromImapFolder(imap: Imap, folderName: string, messages: string[]): Promise<void> {
        let maxRetries = 2; // 文件夹操作的重试次数
        let attempt = 0;

        // 文件夹操作超时控制
        const folderOperationTimeout = setTimeout(() => {
            logger.error(`获取${folderName}文件夹邮件操作超时，中断操作`);
            // 超时时抛出错误，会被外层catch捕获并处理
            throw new Error(`获取${folderName}文件夹邮件操作超时`);
        }, 90000); // 90秒超时

        try {
            while (attempt <= maxRetries) {
                try {
                    logger.info(`打开${folderName}文件夹...(尝试 ${attempt + 1}/${maxRetries + 1})`);

                    // 使用Promise封装文件夹操作
                    return await new Promise<void>((resolve, reject) => {
                        // 打开文件夹
                        imap.openBox(folderName, true, (err, box) => {
                            if (err) {
                                logger.warn(`打开${folderName}文件夹失败:`, err);
                                reject(err);
                                return;
                            }

                            logger.info(`成功打开${folderName}文件夹，包含${box.messages.total}封邮件`);

                            // 如果文件夹为空
                            if (box.messages.total === 0) {
                                logger.info(`${folderName}文件夹为空`);
                                resolve();
                                return;
                            }

                            // 获取最近5封邮件
                            const messagesToFetch = Math.min(5, box.messages.total);
                            const startIndex = Math.max(1, box.messages.total - messagesToFetch + 1);
                            const endIndex = box.messages.total;

                            logger.info(`获取${folderName}文件夹中的最近${messagesToFetch}封邮件`);

                            // 搜索特定时间范围内的邮件
                            const searchCriteria = ['ALL'];

                            // 设置操作超时
                            const operationTimeout = setTimeout(() => {
                                reject(new Error(`获取${folderName}文件夹邮件操作超时`));
                            }, 60000); // 60秒超时

                            // 执行搜索
                            imap.search(searchCriteria, (searchErr, results) => {
                                if (searchErr) {
                                    clearTimeout(operationTimeout);
                                    logger.error(`搜索${folderName}文件夹邮件失败:`, searchErr);
                                    reject(searchErr);
                                    return;
                                }

                                // 如果没有找到邮件
                                if (results.length === 0) {
                                    clearTimeout(operationTimeout);
                                    logger.info(`${folderName}文件夹中没有匹配的邮件`);
                                    resolve();
                                    return;
                                }

                                // 仅获取最近的几封邮件
                                results.sort((a, b) => b - a); // 按ID降序排列
                                const recentIds = results.slice(0, messagesToFetch);

                                // 获取邮件详情
                                const fetch = imap.fetch(recentIds, {
                                    bodies: '',
                                    markSeen: false
                                });

                                // 存储已获取的邮件数量
                                let fetchedCount = 0;
                                const expectedCount = recentIds.length;

                                fetch.on('message', (msg, seqno) => {
                                    logger.debug(`获取${folderName}文件夹中的邮件 #${seqno}`);
                                    let rawEmail = '';

                                    msg.on('body', (stream, info) => {
                                        // 添加流错误处理
                                        stream.on('error', (streamErr) => {
                                            logger.error(`邮件流读取错误: ${streamErr.message}`);
                                        });

                                        stream.on('data', (chunk) => {
                                            rawEmail += chunk.toString('utf8');
                                        });
                                    });

                                    msg.once('error', (msgErr) => {
                                        logger.error(`邮件消息处理错误: ${msgErr.message}`);
                                    });

                                    msg.once('end', () => {
                                        logger.debug(`${folderName}文件夹中的邮件 #${seqno} 获取完成`);
                                        if (rawEmail) {
                                            messages.push(rawEmail);
                                        }
                                        fetchedCount++;

                                        // 如果所有邮件都已获取，但fetch.end事件没触发，手动处理
                                        if (fetchedCount >= expectedCount) {
                                            logger.debug(`所有${fetchedCount}封邮件已获取，等待fetch.end事件...`);
                                            // 不主动触发resolve，让fetch.end事件处理
                                        }
                                    });
                                });

                                fetch.once('error', (fetchErr) => {
                                    clearTimeout(operationTimeout);
                                    logger.error(`获取${folderName}文件夹邮件失败:`, fetchErr);
                                    reject(fetchErr);
                                });

                                fetch.once('end', () => {
                                    clearTimeout(operationTimeout);
                                    logger.info(`已获取${folderName}文件夹中的所有邮件 (${fetchedCount}/${expectedCount})`);
                                    resolve();
                                });
                            });
                        });
                    });
                } catch (error: any) {
                    logger.error(`获取${folderName}文件夹邮件失败 (尝试 ${attempt + 1}/${maxRetries + 1}):`, error);

                    // 判断是否是连接相关错误
                    const isConnectionError = error.code === 'EPIPE' ||
                        error.message?.includes('socket has been ended') ||
                        error.message?.includes('connection closed') ||
                        error.message?.includes('timeout');

                    // 如果是最后一次尝试或不是连接错误
                    if (attempt >= maxRetries || !isConnectionError) {
                        logger.warn(`放弃获取${folderName}文件夹邮件，继续处理`);
                        return; // 不抛出错误，继续流程
                    }

                    // 等待后重试
                    const retryDelay = 3000 * (attempt + 1); // 递增延迟
                    logger.info(`等待 ${retryDelay}ms 后重试获取${folderName}文件夹邮件...`);
                    await this.wait(retryDelay);
                    attempt++;

                    // 尝试重新创建IMAP连接
                    try {
                        logger.info('尝试重新创建IMAP连接...');

                        // 不尝试检查imap状态，直接创建新连接
                        logger.info('放弃旧连接，直接创建新连接');

                        // 安全地关闭旧连接
                        try {
                            (imap as unknown as { end: () => void }).end();
                        } catch (endError) {
                            logger.debug('关闭IMAP连接时出错，忽略', endError);
                        }

                        // 创建新连接
                        imap = await this.createImapConnection();
                    } catch (reconnectError) {
                        logger.error('重新创建IMAP连接失败:', reconnectError);
                        // 继续使用现有连接
                    }
                }
            }
        } finally {
            // 清除文件夹操作超时
            clearTimeout(folderOperationTimeout);
        }
    }

    /**
     * 删除IMAP邮箱中的所有邮件
     * @param folderNames 要删除邮件的文件夹数组（不指定则使用INBOX和找到的垃圾邮件文件夹）
     */
    private async deleteAllImapEmails(folderNames?: string[]): Promise<boolean> {
        // 将方法体替换为临时实现
        let imap: Imap | null = null;

        // 添加超时
        const timeoutPromise = new Promise<boolean>((_, reject) => {
            setTimeout(() => {
                logger.error('删除邮件操作超时，强制终止');
                reject(new Error('删除邮件操作超时'));
            }, 2 * 60 * 1000); // 2分钟超时
        });

        // 删除邮件的实际操作
        const deletePromise = (async () => {
            try {
                imap = await this.createImapConnection();

                // 获取文件夹列表
                let foldersToProcess: string[] = [];

                if (folderNames && folderNames.length > 0) {
                    // 使用指定的文件夹
                    foldersToProcess = folderNames;
                } else {
                    // 尝试获取文件夹列表
                    try {
                        const folders = await this.getMailboxFolders(imap);

                        // 至少包含收件箱
                        foldersToProcess.push('INBOX');

                        // 添加垃圾邮件文件夹
                        const junk = this.findJunkFolder(folders);
                        if (junk) {
                            foldersToProcess.push(junk);
                        }
                    } catch (error) {
                        // 如果获取文件夹失败，至少处理收件箱
                        logger.error('获取文件夹失败，仅使用INBOX:', error);
                        foldersToProcess = ['INBOX'];
                    }
                }

                // 处理每个文件夹
                for (const folder of foldersToProcess) {
                    try {
                        // 添加文件夹操作超时
                        await Promise.race([
                            this.deleteMessagesFromImapFolder(imap, folder),
                            new Promise<void>((_, folderReject) => {
                                setTimeout(() => folderReject(new Error(`删除${folder}文件夹超时`)), 60000);
                            })
                        ]);
                    } catch (error) {
                        logger.error(`删除${folder}文件夹邮件失败:`, error);
                        // 继续处理其他文件夹
                    }
                }

                return true;
            } catch (error) {
                logger.error('删除邮件失败:', error);
                return false;
            } finally {
                // 关闭连接
                if (imap) {
                    try {
                        (imap as unknown as { end: () => void }).end();
                    } catch (error) {
                        // 忽略错误
                    }
                }
            }
        })();

        // 使用Promise.race选择先完成的操作
        try {
            return await Promise.race([deletePromise, timeoutPromise]);
        } catch (error) {
            // 确保超时情况下连接被关闭
            if (imap) {
                try {
                    (imap as unknown as { end: () => void }).end();
                } catch (error) {
                    // 忽略错误
                }
            }
            return false;
        }
    }

    /**
     * 从IMAP指定文件夹删除所有邮件
     * @param imap IMAP连接
     * @param folderName 文件夹名称
     */
    private deleteMessagesFromImapFolder(imap: Imap, folderName: string): Promise<void> {
        return new Promise((resolve, reject) => {
            logger.info(`打开${folderName}文件夹准备删除邮件...`);

            // 打开文件夹，可写模式
            imap.openBox(folderName, false, (err, box) => {
                if (err) {
                    logger.warn(`打开${folderName}文件夹失败:`, err);
                    resolve(); // 即使失败也继续流程
                    return;
                }

                logger.info(`成功打开${folderName}文件夹，包含${box.messages.total}封邮件待删除`);

                // 如果文件夹为空
                if (box.messages.total === 0) {
                    logger.info(`${folderName}文件夹为空，无需删除`);
                    resolve();
                    return;
                }

                // 搜索所有邮件
                imap.search(['ALL'], (searchErr, results) => {
                    if (searchErr) {
                        logger.error(`搜索${folderName}文件夹邮件失败:`, searchErr);
                        resolve(); // 继续流程
                        return;
                    }

                    // 如果没有找到邮件
                    if (results.length === 0) {
                        logger.info(`${folderName}文件夹中没有邮件可删除`);
                        resolve();
                        return;
                    }

                    logger.info(`标记删除${folderName}文件夹中的${results.length}封邮件`);

                    // 添加删除标记
                    imap.addFlags(results, '\\Deleted', (flagErr) => {
                        if (flagErr) {
                            logger.error(`标记删除${folderName}文件夹邮件失败:`, flagErr);
                            resolve(); // 继续流程
                            return;
                        }

                        // 执行删除
                        imap.expunge((expungeErr) => {
                            if (expungeErr) {
                                logger.error(`执行删除${folderName}文件夹邮件失败:`, expungeErr);
                            } else {
                                logger.info(`成功删除${folderName}文件夹中的${results.length}封邮件`);
                            }
                            resolve();
                        });
                    });
                });
            });
        });
    }

    /**
     * Extract OpenAI verification code from email content
     */
    private async extractVerificationCode(emailContent: string): Promise<string | null> {
        try {
            // Parse the email using mailparser
            const parsed = await simpleParser(emailContent);

            // 检查是否是OpenAI的邮件
            const subject = parsed.subject || '';
            const text = parsed.text || '';
            const html = parsed.html || '';

            // 组合所有内容用于搜索
            const fullContent = `${subject} ${text} ${html}`.toLowerCase();

            // 如果不是OpenAI的邮件，直接返回null
            if (!fullContent.includes('openai')) {
                return null;
            }

            // 如果是OpenAI的邮件，继续提取验证码
            // Pattern 1: 6-digit code (most common)
            const sixDigitMatch = fullContent.match(/(?:code|verification code|verify your email)[^0-9]*(\d{6})/i);
            if (sixDigitMatch && sixDigitMatch[1]) {
                return sixDigitMatch[1];
            }

            // Pattern 2: HTML email with code in a specific format
            const htmlCodeMatch = html.match(/<td[^>]*>\s*(\d{6})\s*<\/td>/i);
            if (htmlCodeMatch && htmlCodeMatch[1]) {
                return htmlCodeMatch[1];
            }

            // Pattern 3: General digit search for OpenAI
            const digitMatch = fullContent.match(/OpenAI[^\d]*(\d{6})/i);
            if (digitMatch && digitMatch[1]) {
                return digitMatch[1];
            }

            // Pattern 4: Any six digit number if we're desperate
            const anyDigitMatch = fullContent.match(/(\d{6})/);
            if (anyDigitMatch && anyDigitMatch[1]) {
                logger.debug(`Found potential code ${anyDigitMatch[1]} but not in expected context`);
                if (fullContent.includes('verification') || fullContent.includes('code')) {
                    return anyDigitMatch[1];
                }
            }

            return null;
        } catch (error) {
            logger.error('Error extracting verification code:', error);
            return null;
        }
    }

    /**
     * Get verification code from Microsoft email
     * 使用IMAP协议获取邮件，包括垃圾邮件文件夹
     */
    async getVerificationCode(
        fromTime: Date = new Date(Date.now() - 10 * 60 * 1000), // Last 10 minutes by default
        maxAttempts: number = 5,
        delayBetweenAttempts: number = 10000
    ): Promise<VerificationResult> {
        let attempts = 0;
        const emailHelper = EmailHelper.getInstance();
        let foundCode = false;
        let verificationCode = '';
        let lastError: any = null;

        // Ensure we're authenticated
        if (!this.accessToken) {
            const authenticated = await this.authenticate();
            if (!authenticated) {
                return { success: false, error: 'Failed to authenticate with Microsoft' };
            }
        }

        // 设置全局超时，防止整个流程卡住
        const globalTimeout = setTimeout(() => {
            logger.error('获取验证码操作全局超时，强制结束');
            lastError = new Error('获取验证码操作全局超时');
        }, 5 * 60 * 1000); // 5分钟超时

        try {
            while (attempts < maxAttempts && !lastError) {
                let imap: Imap | null = null;

                try {
                    logger.info(`尝试获取验证码 (第${attempts + 1}次)`);

                    // 使用IMAP获取邮件（收件箱和垃圾邮件文件夹）
                    const messages = await this.getMessagesViaIMAP();

                    if (messages.length === 0) {
                        logger.info('邮箱中没有邮件');
                        attempts++;

                        if (attempts < maxAttempts && !lastError) {
                            logger.info(`等待 ${delayBetweenAttempts / 1000} 秒后再次尝试`);
                            await this.wait(delayBetweenAttempts);
                        }
                        continue;
                    }

                    // 按时间排序，最新的邮件在前
                    const sortedMessages = await Promise.all(messages.map(async (message) => {
                        const parsed = await simpleParser(message);
                        return {
                            message,
                            date: parsed.date || new Date(0)
                        };
                    }));

                    sortedMessages.sort((a, b) => b.date.getTime() - a.date.getTime());

                    // 只处理最新的5封邮件
                    const recentMessages = sortedMessages.slice(0, 5);

                    // Search for verification code in messages
                    for (const { message } of recentMessages) {
                        try {
                            const code = await this.extractVerificationCode(message);
                            if (code) {
                                logger.info(`找到验证码: ${code}`);

                                // 保存包含验证码的邮件
                                await emailHelper.saveVerificationEmail(
                                    message,
                                    'microsoft',
                                    code
                                );

                                // 设置找到验证码的标志和验证码
                                foundCode = true;
                                verificationCode = code;
                                break;
                            }
                        } catch (parseError) {
                            logger.error('解析邮件错误:', parseError);
                            // Continue with next message
                        }
                    }

                    // 如果找到了验证码，尝试删除所有邮件
                    if (foundCode) {
                        logger.info('已找到验证码，正在删除所有邮件...');
                        try {
                            await this.deleteAllImapEmails();
                        } catch (deleteError) {
                            logger.warn('删除邮件失败，但已找到验证码，继续流程:', deleteError);
                        }
                        return { success: true, code: verificationCode };
                    }

                    // If we reach here, no code was found in this attempt
                    logger.info(`第 ${attempts + 1} 次尝试未找到验证码`);
                    attempts++;

                    // Wait before next attempt
                    if (attempts < maxAttempts && !lastError) {
                        logger.info(`等待 ${delayBetweenAttempts / 1000} 秒后再次尝试`);
                        await this.wait(delayBetweenAttempts);
                    }
                } catch (error: any) {
                    lastError = error;
                    logger.error(`第 ${attempts + 1} 次尝试中发生错误:`, error);
                    attempts++;

                    // 增加延迟时间，指数退避策略
                    delayBetweenAttempts = Math.min(delayBetweenAttempts * 1.5, 30000);

                    // Wait before next attempt
                    if (attempts < maxAttempts && !lastError) {
                        logger.info(`等待增加的延迟 ${delayBetweenAttempts / 1000} 秒后重试...`);
                        await this.wait(delayBetweenAttempts);
                    }
                } finally {
                    if (imap) {
                        try {
                            (imap as unknown as { end: () => void }).end();
                        } catch (endError) {
                            // 忽略关闭连接时的错误
                        }
                    }
                }
            }

            // 最后一次尝试删除所有邮件
            try {
                logger.info('验证码获取失败，尝试清理邮箱...');
                await this.deleteAllImapEmails();
            } catch (error) {
                logger.error('清理邮箱失败:', error);
            }

            const errorMessage = lastError ?
                `经过 ${maxAttempts} 次尝试后未找到验证码。最后错误: ${lastError.message || JSON.stringify(lastError)}` :
                `经过 ${maxAttempts} 次尝试后未找到验证码`;

            return { success: false, error: errorMessage };
        } finally {
            // 清除全局超时
            clearTimeout(globalTimeout);
        }
    }
}
