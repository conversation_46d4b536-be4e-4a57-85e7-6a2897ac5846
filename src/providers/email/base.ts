/**
 * Base interface for all email providers
 * Each email provider must implement these methods
 */
export interface EmailCredentials {
    email: string;
    password?: string;
    clientId?: string;
    clientSecret?: string;
    refreshToken?: string;
    accessToken?: string;
    host?: string;
    port?: number;
    protocol?: string;
    domain?: string;
    tempMail?: string;
    tempMailEpin?: string;
    tempMailExt?: string;
    epin?: string;
    // 微软邮箱别名相关属性
    useAlias?: boolean;
    aliasEmail?: string;
    aliasPrefix?: string;
    aliasRandomLength?: number;
}

export interface VerificationResult {
    success: boolean;
    code?: string;
    error?: string;
}

export abstract class BaseEmailProvider {
    protected credentials: EmailCredentials;

    constructor(credentials: EmailCredentials) {
        this.credentials = credentials;
    }

    /**
     * Authenticate with the email provider
     */
    abstract authenticate(): Promise<boolean>;

    /**
     * Check for verification emails and extract the verification code
     * @param fromTime Only check emails after this time (optional)
     * @param maxAttempts Maximum number of attempts to find the code
     * @param delayBetweenAttempts Delay between attempts in milliseconds
     */
    abstract getVerificationCode(
        fromTime?: Date,
        maxAttempts?: number,
        delayBetweenAttempts?: number
    ): Promise<VerificationResult>;

    /**
     * Helper method to wait for a specified time
     * @param ms Time to wait in milliseconds
     */
    protected async wait(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
