import { BaseEmailProvider, EmailCredentials, VerificationResult } from './base';
import { google, gmail_v1 } from 'googleapis';
import { logger } from '../../utils/logger';

/**
 * Gmail email provider implementation
 * Uses Google APIs to authenticate and retrieve emails
 */
export class GmailEmailProvider extends BaseEmailProvider {
    private gmail: gmail_v1.Gmail | null = null;

    constructor(credentials: EmailCredentials) {
        super(credentials);
        if (!credentials.clientId || !credentials.clientSecret || !credentials.refreshToken) {
            throw new Error('Gmail provider requires clientId, clientSecret, and refreshToken');
        }
    }

    /**
     * Authenticate with Gmail using OAuth2
     */
    async authenticate(): Promise<boolean> {
        try {
            const { clientId, clientSecret, refreshToken } = this.credentials;

            if (!clientId || !clientSecret || !refreshToken) {
                throw new Error('Missing required credentials: clientId, clientSecret, or refreshToken');
            }

            const oauth2Client = new google.auth.OAuth2(
                clientId,
                clientSecret
            );

            oauth2Client.setCredentials({
                refresh_token: refreshToken
            });

            this.gmail = google.gmail({ version: 'v1', auth: oauth2Client });
            logger.info('Successfully authenticated with Gmail');
            return true;
        } catch (error) {
            logger.error('Gmail authentication failed:', error);
            return false;
        }
    }

    /**
     * Decode base64 encoded message parts
     */
    private decodeBase64(encoded: string): string {
        // Handle URL-safe base64 encoding
        const sanitized = encoded.replace(/-/g, '+').replace(/_/g, '/');
        return Buffer.from(sanitized, 'base64').toString('utf-8');
    }

    /**
     * Extract OpenAI verification code from email content
     */
    private extractVerificationCode(emailContent: string): string | null {
        try {
            // Look for common patterns in OpenAI verification emails

            // Pattern 1: 6-digit code (most common)
            const sixDigitMatch = emailContent.match(/(?:code|verification code|verify your email)[^0-9]*(\d{6})/i);
            if (sixDigitMatch && sixDigitMatch[1]) {
                return sixDigitMatch[1];
            }

            // Pattern 2: HTML email with code in a specific format
            const htmlCodeMatch = emailContent.match(/<td[^>]*>\s*(\d{6})\s*<\/td>/i);
            if (htmlCodeMatch && htmlCodeMatch[1]) {
                return htmlCodeMatch[1];
            }

            // Pattern 3: General digit search (last resort)
            const digitMatch = emailContent.match(/OpenAI[^\d]*(\d{6})/i);
            if (digitMatch && digitMatch[1]) {
                return digitMatch[1];
            }

            return null;
        } catch (error) {
            logger.error('Error extracting verification code:', error);
            return null;
        }
    }

    /**
     * Get verification code from Gmail
     */
    async getVerificationCode(
        fromTime: Date = new Date(Date.now() - 10 * 60 * 1000), // Last 10 minutes by default
        maxAttempts: number = 5,
        delayBetweenAttempts: number = 10000
    ): Promise<VerificationResult> {
        let attempts = 0;

        // Ensure we're authenticated
        if (!this.gmail) {
            const authenticated = await this.authenticate();
            if (!authenticated) {
                return { success: false, error: 'Failed to authenticate with Gmail' };
            }
        }

        // Format the date for Gmail query
        const fromDate = fromTime.toISOString().substring(0, 10);

        while (attempts < maxAttempts) {
            try {
                logger.info(`Attempt ${attempts + 1} to get verification code`);

                // Search for emails from OpenAI
                const response = await this.gmail!.users.messages.list({
                    userId: 'me',
                    q: `from:<EMAIL> after:${fromDate} subject:verification`
                });

                if (!response.data.messages || response.data.messages.length === 0) {
                    logger.info('No verification emails found');
                    attempts++;

                    if (attempts < maxAttempts) {
                        logger.info(`Waiting ${delayBetweenAttempts / 1000} seconds before next attempt`);
                        await this.wait(delayBetweenAttempts);
                    }
                    continue;
                }

                // Process the most recent messages first
                for (const message of response.data.messages) {
                    const email = await this.gmail!.users.messages.get({
                        userId: 'me',
                        id: message.id!
                    });

                    // Extract email content
                    const parts = email.data.payload?.parts;
                    let emailContent = '';

                    if (parts && parts.length > 0) {
                        for (const part of parts) {
                            if (part.mimeType === 'text/plain' || part.mimeType === 'text/html') {
                                if (part.body?.data) {
                                    emailContent += this.decodeBase64(part.body.data);
                                }
                            }
                        }
                    } else if (email.data.payload?.body?.data) {
                        // Handle single-part emails
                        emailContent = this.decodeBase64(email.data.payload.body.data);
                    }

                    // Extract verification code
                    const code = this.extractVerificationCode(emailContent);
                    if (code) {
                        logger.info(`Found verification code: ${code}`);
                        return { success: true, code };
                    }
                }

                // If we reach here, no code was found in this attempt
                logger.info(`No verification code found in attempt ${attempts + 1}`);
                attempts++;

                // Wait before next attempt
                if (attempts < maxAttempts) {
                    logger.info(`Waiting ${delayBetweenAttempts / 1000} seconds before next attempt`);
                    await this.wait(delayBetweenAttempts);
                }
            } catch (error) {
                logger.error(`Error in attempt ${attempts + 1}:`, error);
                attempts++;

                // Wait before next attempt
                if (attempts < maxAttempts) {
                    await this.wait(delayBetweenAttempts);
                }
            }
        }

        return { success: false, error: `Failed to find verification code after ${maxAttempts} attempts` };
    }
}
