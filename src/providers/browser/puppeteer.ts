import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import {<PERSON><PERSON><PERSON>, <PERSON>} from 'puppeteer';
import {logger} from '../../utils/logger';
import {PageHelper} from '../../utils/page-helper';
import config from "../../config";
import path from "path";

// Add stealth plugin to avoid detection
puppeteer.use(StealthPlugin());

/**
 * OpenAI registration form data
 */
export interface OpenAIRegistrationData {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    dateOfBirth: {
        day: string;
        month: string;
        year: string;
    };
    country: string;
    phoneNumber?: string;
}

/**
 * Browser automation service using Puppeteer
 */
export class PuppeteerBrowser {
    private browser: Browser | null = null;
    private page: Page | null = null;
    private helper: PageHelper | null = null;
    private isExtractingAPIKey: boolean = false; // 添加标志位控制是否在提取API密钥

    /**
     * Initialize browser with stealth options
     */
    async initialize(headless: boolean = true): Promise<boolean> {
        try {
            logger.info('正在初始化浏览器...');
            console.log('[浏览器] 尝试启动Puppeteer...');

            // 获取代理配置
            const proxyEnabled = config.get('browser.proxy.enabled');
            const proxyServer = config.get('browser.proxy.server');
            const proxyType = config.get('browser.proxy.type');
            let proxyUsername = config.get('browser.proxy.username');
            let proxyPassword = config.get('browser.proxy.password');

            // 解析代理服务器地址，提取用户名和密码
            let cleanProxyServer = proxyServer;
            if (proxyEnabled && proxyServer) {
                try {
                    const url = new URL(proxyServer);
                    if (url.username && url.password) {
                        // 如果URL中包含用户名和密码，提取它们并进行URL解码
                        proxyUsername = proxyUsername || decodeURIComponent(url.username);
                        proxyPassword = proxyPassword || decodeURIComponent(url.password);
                        // 构建不包含认证信息的代理服务器地址
                        cleanProxyServer = `${url.protocol}//${url.host}`;
                        logger.info('从代理URL中提取认证信息');
                        console.log('[浏览器] 提取代理认证信息，用户名:', proxyUsername);
                        console.log('[浏览器] 清理后的代理地址:', cleanProxyServer);
                    }
                } catch (error) {
                    logger.warn('代理URL解析失败，使用原始地址:', error);
                    console.warn('[浏览器] 代理URL解析失败:', error);
                    // 如果URL解析失败，尝试直接使用原始地址（可能是简单的host:port格式）
                    cleanProxyServer = proxyServer;
                }
            }

            // 从环境变量中获取额外的启动参数
            const envArgs = process.env.PUPPETEER_ARGS ? process.env.PUPPETEER_ARGS.split(' ') : [];
            const defaultArgs = [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--disable-gpu',
                '--window-size=1920,1080',
                '--disable-features=site-per-process',
                '--disable-extensions',
                '--disable-background-networking',
                // 添加CSP绕过参数
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process',
                '--disable-site-isolation-trials'
            ];

            // 检查代理配置
            if (proxyEnabled && proxyServer) {
                logger.info('启用代理配置:', cleanProxyServer);
                console.log('[浏览器] 使用代理:', cleanProxyServer);
                defaultArgs.push(`--proxy-server=${cleanProxyServer}`);

                // 如果是SOCKS5代理，添加相关参数
                if (proxyType === 'socks5') {
                    defaultArgs.push('--host-resolver-rules=MAP * ~NOTFOUND, EXCLUDE localhost');
                }
            }

            // 合并环境变量中的参数和默认参数，去重
            const args = [...new Set([...defaultArgs, ...envArgs])];

            // 准备Puppeteer启动配置
            const launchOptions: any = {
                headless: headless, // 使用布尔值替代字符串，增加兼容性
                executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
                args: args,
                defaultViewport: {
                    width: 1920,
                    height: 1080
                },
                timeout: 60000, // 增加超时时间到 60 秒
            };

            // 尝试使用兼容性更好的配置
            this.browser = await puppeteer.launch(launchOptions);

            console.log('[浏览器] Puppeteer 启动成功，创建新页面...');
            this.page = await this.browser.newPage();

            // 确保页面被创建
            if (!this.page) {
                throw new Error('Failed to create page');
            }

            // 设置代理认证（如果需要）
            if (proxyEnabled && proxyUsername && proxyPassword) {
                logger.info('设置代理认证...');
                console.log('[浏览器] 设置代理认证，用户名:', proxyUsername);
                await this.page.authenticate({
                    username: proxyUsername,
                    password: proxyPassword
                });
            }

            // 创建页面助手实例
            this.helper = new PageHelper(this.page);
            console.log('[浏览器] 页面助手已初始化');

            // 设置用户代理到更现代的浏览器版本
            await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

            // 禁用内容安全策略
            await this.page.setBypassCSP(true);

            // 启用请求拦截，但提供更详细的错误处理
            try {
                console.log('[浏览器] 设置请求拦截...');
                await this.page.setRequestInterception(true);

                this.page.on('request', (request) => {
                    try {
                        const resourceType = request.resourceType();

                        // 根据配置决定是否阻止图片、字体、媒体等资源类型，节约流量
                        // 但在提取API密钥时不进行拦截，确保页面正常显示
                        const optimizeBandwidth = config.get('browser.optimizeBandwidth', false);
                        if (optimizeBandwidth && !this.isExtractingAPIKey && ['image', 'font', 'media', 'other', 'stylesheet'].includes(resourceType)) {
                            console.log(`[流量优化] 阻止加载 ${resourceType}: ${request.url()}`);
                            request.abort();
                            return;
                        }

                        // 修改请求以禁用CSP
                        if (resourceType === 'document') {
                            let headers = request.headers();
                            headers['Content-Security-Policy'] = '';
                            request.continue({ headers });
                        } else {
                            request.continue();
                        }
                    } catch (e) {
                        console.error('[浏览器] 请求拦截错误:', e);
                        try {
                            request.abort();
                        } catch {
                            // 忽略 abort 可能的错误
                        }
                    }
                });

                this.page.on('error', (err) => {
                    console.error('[浏览器] 页面错误:', err);
                });

                this.page.on('pageerror', (err) => {
                    console.error('[浏览器] 页面JavaScript错误:', err);
                });
            } catch (err) {
                console.warn('[浏览器] 无法设置请求拦截，继续执行:', err);
            }

            logger.info('浏览器初始化成功');
            return true;
        } catch (error: any) {
            logger.error('浏览器初始化失败:', error);
            console.error('[浏览器] 初始化错误详情:', error);

            // 尝试优雅地关闭任何可能已打开的资源
            try {
                if (this.page) {
                    await this.page.close();
                }
                if (this.browser) {
                    await this.browser.close();
                }
            } catch {
                // 忽略关闭时的错误
            }

            // 检查是否是"Cannot fork"或"spawn_subprocess"错误
            const errorStr = error.toString();
            if (errorStr.includes('Cannot fork') ||
                errorStr.includes('spawn_subprocess') ||
                errorStr.includes('posix_spawn') ||
                errorStr.includes('Failed to launch')) {
                console.error('[浏览器] 检测到致命错误: 无法创建Chrome进程。正在终止程序...');
                logger.error('检测到Chrome进程创建失败，自动退出程序');
                // 等待日志写入
                setTimeout(() => {
                    process.exit(1);  // 异常退出，使Docker重启容器
                }, 1000);
            }

            return false;
        }
    }

    /**
     * Navigate to OpenAI registration page
     */
    async navigateToRegistration(): Promise<boolean> {
        if (!this.page) {
            logger.error('Browser not initialized');
            return false;
        }

        // 如果 helper 为 null，创建一个新的 PageHelper 实例
        if (!this.helper) {
            logger.warn('PageHelper not initialized, creating new instance');
            this.helper = new PageHelper(this.page);
        }

        try {
            logger.info('Navigating to OpenAI registration page');
            await this.page.goto('https://platform.openai.com/signup', {
                waitUntil: 'networkidle2',
                timeout: 60000
            });

            // 使用页面助手等待注册表单加载
            const emailInputExists = await this.helper.waitForElement('input[type="email"]', 30000);
            if (!emailInputExists) {
                logger.error('Registration form failed to load');
                return false;
            }

            return true;
        } catch (error) {
            logger.error('Failed to navigate to registration page:', error);
            return false;
        }
    }

    /**
     * Fill registration form with user data
     */
    async fillRegistrationForm(data: OpenAIRegistrationData): Promise<boolean> {
        if (!this.page) {
            logger.error('Browser not initialized');
            return false;
        }

        // 如果 helper 为 null，创建一个新的 PageHelper 实例
        if (!this.helper) {
            logger.warn('PageHelper not initialized, creating new instance');
            this.helper = new PageHelper(this.page);
        }

        try {
            logger.info('Filling registration form');

            // 启动错误监控（不等待它完成）
            this.helper.monitorErrorAndRetry(undefined,undefined,10000).catch(error => {
                logger.error('错误监控发生异常:', error);
            });

            // Step 1: 输入电子邮件
            logger.info('Step 1: 输入电子邮件');
            if (!await this.helper.type('input[type="email"]', data.email, {delay: 30})) {
                logger.error('Could not find email input field');
                return false;
            }

            // 提交电子邮件
            if (!await this.helper.click('button[type="submit"]')) {
                logger.warn('未找到submit按钮，尝试按回车键');
                await this.helper.pressEnter();
            }

            // 等待导航完成
            await this.helper.waitForNavigation();

            // Step 2: 选择继续使用电子邮件（如果出现）
            // logger.info('Step 2: 选择继续使用电子邮件（如果出现）');
            // const continueWithEmailExists = await this.helper.textExists("Continue with Email", "button");
            // if (continueWithEmailExists) {
            //     if (!await this.helper.clickByText("Continue with Email", "button")) {
            //         if (!await this.helper.clickByTextWithEval("Continue with Email")) {
            //             logger.warn('Could not click "Continue with Email" button, trying to proceed anyway');
            //         }
            //     }
            //     await this.helper.waitForNavigation();
            // }

            // Step 3: 输入密码
            logger.info('Step 3: 输入密码');
            const passwordFieldExists = await this.helper.exists('input[type="password"]');
            if (passwordFieldExists) {
                await this.helper.type('input[type="password"]', data.password, {delay: 30});

                // 尝试找到并点击continue按钮
                if (await this.helper.textExists("Continue", "button")) {
                    // 如果找到了"Continue"按钮，点击它
                    await this.helper.clickByText("Continue", "button");
                } else {
                    // 如果找不到按钮，尝试按回车键提交表单
                    logger.info('尝试按回车键提交密码表单');
                    await this.helper.pressEnter();
                }

                await this.helper.waitForNavigation();
            } else {
                logger.warn('未找到密码输入框');
            }

            logger.info('基础注册表单已提交，等待邮箱验证码');
            return true;
        } catch (error) {
            logger.error('Failed to fill registration form:', error);
            return false;
        }
    }

    /**
     * Enter verification code received via email
     */
    async enterVerificationCode(code: string): Promise<boolean> {
        if (!this.page) {
            logger.error('Browser not initialized');
            return false;
        }

        // 如果 helper 为 null，创建一个新的 PageHelper 实例
        if (!this.helper) {
            logger.warn('PageHelper not initialized, creating new instance');
            this.helper = new PageHelper(this.page);
        }

        try {
            logger.info(`Entering verification code: ${code}`);

            // 等待验证码输入框出现
            if (!await this.helper.waitForElement('input[type="text"]', 30000)) {
                logger.error('Verification code input field not found');

                // 尝试截图用于调试
                await this.takeScreenshot('verification-code-error.png');
                return false;
            }

            // 输入验证码
            await this.helper.type('input[type="text"]', code, {delay: 30});

            // 提交验证码
            let verifySuccess = false;

            // 尝试方法1：提交按钮
            if (await this.helper.exists('button[type="submit"]')) {
                verifySuccess = await this.helper.click('button[type="submit"]');
            }

            // 尝试方法2：查找"Verify"或"Continue"按钮
            if (!verifySuccess && await this.helper.textExists("Verify", "button")) {
                verifySuccess = await this.helper.clickByText("Verify", "button");
            }

            if (!verifySuccess && await this.helper.textExists("Continue", "button")) {
                verifySuccess = await this.helper.clickByText("Continue", "button");
            }

            // 尝试方法3：按回车键
            if (!verifySuccess) {
                logger.info('尝试按回车键提交验证码');
                verifySuccess = await this.helper.pressEnter();
            }

            if (!verifySuccess) {
                logger.warn('无法通过任何方式提交验证码，尝试继续流程');
            }

            // 等待导航完成
            await this.helper.waitForNavigation();

            logger.info('Verification code submitted successfully');
            return true;
        } catch (error) {
            logger.error('Failed to enter verification code:', error);
            return false;
        }
    }

    /**
     * 填写个人信息表单
     */
    async fillPersonalInfoForm(data: OpenAIRegistrationData): Promise<boolean> {
        if (!this.page) {
            logger.error('Browser not initialized');
            return false;
        }

        // 如果 helper 为 null，创建一个新的 PageHelper 实例
        if (!this.helper) {
            logger.warn('PageHelper not initialized, creating new instance');
            this.helper = new PageHelper(this.page);
        }

        try {
            logger.info('开始填写个人信息表单');

            // 等待页面加载 - 等待全名输入框出现
            await this.helper.waitForElement('input[name="name"]', 30000);

            // 填写全名（合并 firstName 和 lastName）
            const fullName = `${data.firstName} ${data.lastName}`;
            if (await this.helper.exists('input[name="name"]')) {
                await this.helper.type('input[name="name"]', fullName, {delay: 30});
            } else {
                logger.warn('未找到全名输入框');
            }

            // 填写生日
            // 月份
            const monthSpinbutton = await this.page.$('[role="spinbutton"][aria-label*="month"]');
            if (monthSpinbutton) {
                await monthSpinbutton.focus();
                await this.page.keyboard.type(data.dateOfBirth.month.padStart(2, '0'), {delay: 30});
            } else {
                logger.warn('未找到月份输入框');
            }

            // 日期
            const daySpinbutton = await this.page.$('[role="spinbutton"][aria-label*="day"]');
            if (daySpinbutton) {
                await daySpinbutton.focus();
                await this.page.keyboard.type(data.dateOfBirth.day.padStart(2, '0'), {delay: 30});
            } else {
                logger.warn('未找到日期输入框');
            }

            // 年份
            const yearSpinbutton = await this.page.$('[role="spinbutton"][aria-label*="year"]');
            if (yearSpinbutton) {
                await yearSpinbutton.focus();
                await this.page.keyboard.type(data.dateOfBirth.year, {delay: 30});
            } else {
                logger.warn('未找到年份输入框');
            }

            // 提交表单
            let submitSuccess = false;

            // 尝试点击 Continue 按钮
            if (await this.helper.textExists("Continue", "button")) {
                submitSuccess = await this.helper.clickByText("Continue", "button");
            }

            // 如果上述方法失败，尝试按回车键
            if (!submitSuccess) {
                logger.info('尝试按回车键提交表单');
                submitSuccess = await this.helper.pressEnter();
            }

            if (!submitSuccess) {
                logger.warn('无法通过任何方式提交表单，尝试继续流程');
            }

            await this.helper.waitForNavigation();

            logger.info('个人信息表单已成功提交');
            return true;
        } catch (error) {
            logger.error('填写个人信息表单失败:', error);
            return false;
        }
    }

    /**
     * Extract API key after successful registration using the new OpenAI interface
     */
    async extractAPIKey(): Promise<string | null> {
        // 设置标志位，禁用流量优化以确保API密钥页面正常加载
        this.isExtractingAPIKey = true;
        console.log('[API密钥提取] 已禁用流量优化，确保页面正常加载');

        // 多等待几秒钟
        await new Promise(resolve => setTimeout(resolve, 5000));

        // 只在启用截图保存时才截图
        if (config.get('debug.saveScreenshots', true)) {
            this.takeScreenshot(path.join(
                    config.get('browser.screenshotsDir'),
                    `api-key-extraction_${Date.now()}.png`
                )).catch(() => {
                logger.error('Failed to take screenshot for API key extraction');
            });
        }
        if (!this.page) {
            logger.error('Browser not initialized');
            this.isExtractingAPIKey = false; // 重置标志位
            return null;
        }

        // Initialize PageHelper if needed
        if (!this.helper) {
            logger.warn('PageHelper not initialized, creating new instance');
            this.helper = new PageHelper(this.page);
        }

        try {
            logger.info('Extracting API key with new OpenAI interface');

            // 判断页面有无error文字如果有,需要打印
            const errorTextExists = await this.helper.visibleTextExists("error", "div");
            if (errorTextExists) {
                const errorText = await this.helper.getText("div");
                logger.error('Error text found on page:\n' + errorText);
                console.error(errorText);
            }

            // Navigate to the new API keys page
            await this.page.goto('https://platform.openai.com/settings/organization/api-keys', {
                waitUntil: 'networkidle2',
                timeout: 60000
            });

            // Wait for page to load
            await this.helper.waitForElement('button[data-variant="solid"]', 30000);

            // Find and click "Create new secret key" button
            const createButtonSelector = 'button[data-variant="solid"]';
            let createButtonClicked = false;

            if (await this.helper.exists(createButtonSelector)) {
                createButtonClicked = await this.helper.click(createButtonSelector);
            }

            if (!createButtonClicked) {
                logger.error('Could not click "Create new secret key" button');
                this.isExtractingAPIKey = false; // 重置标志位
                return null;
            }

            // Wait for name input in modal
            // await this.helper.waitForElement('input[placeholder="Name (optional)"]', 10000);

            // Enter key name
            // await this.helper.type('input[placeholder="Name (optional)"]', 'My Test Key');

            // Wait for dropdown to open
            await new Promise(resolve => setTimeout(resolve, 500));

            // 点击Service account文字
            await this.helper.clickByVisibleText('Service account');
            if (await this.helper.exists('span[aria-haspopup="dialog"]')) {
                // 用鼠标点击Select project...
                await this.helper.clickWithMouse('span[aria-haspopup="dialog"]');
            }
            // Select "Default project" from dropdown using mouse simulation
            const defaultProjectSelector = '.fsluc';
            await new Promise(resolve => setTimeout(resolve, 200));
            if (await this.helper.exists(defaultProjectSelector)) {
                // 使用新的鼠标模拟点击方法
                await this.helper.clickByTextWithMouse("Default project", "div");
                // 等待下拉菜单关闭
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Click the Create secret key button in the modal
            let finalButtonClicked = false;

            // Try by submit type
            if (await this.helper.exists('button[type="submit"]')) {
                finalButtonClicked = await this.helper.click('button[type="submit"]');
            }

            // Try by text content
            if (!finalButtonClicked) {
                finalButtonClicked = await this.helper.clickByText("Create secret key", "button");
            }

            // Try via JavaScript evaluation
            if (!finalButtonClicked) {
                finalButtonClicked = await this.helper.evaluate(() => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    const createButton = buttons.find(button =>
                        button.textContent?.includes('Create secret key'));
                    if (createButton) {
                        createButton.click();
                        return true;
                    }
                    return false;
                });
            }

            if (!finalButtonClicked) {
                logger.error('Could not click final "Create secret key" button');
                this.isExtractingAPIKey = false; // 重置标志位
                return null;
            }

            // Wait for the API key to appear in an input field
            await this.helper.waitForElement('input[readonly][type="text"]', 10000);

            // Extract the API key from the input field
            const apiKey = await this.helper.evaluate(() => {
                const input = document.querySelector('input[readonly][type="text"]') as HTMLInputElement | null;
                return input ? input.value : null;
            });

            if (!apiKey) {
                logger.error('API key value is empty');
                this.isExtractingAPIKey = false; // 重置标志位
                return null;
            }

            logger.info('API key extracted successfully');
            this.isExtractingAPIKey = false; // 重置标志位
            return apiKey;
        } catch (error) {
            logger.error('Error extracting API key:', error);
            // 只在启用截图保存时才截图
            if (config.get('debug.saveScreenshots', true)) {
                await this.takeScreenshot('api-key-extraction-error.png');
            }
            this.isExtractingAPIKey = false; // 重置标志位
            return null;
        }
    }

    /**
     * Extract API key after successful registration using the new OpenAI interface
     */
    async extractAPIKeyPro(): Promise<string | null> {
        // 设置标志位，禁用流量优化以确保API密钥页面正常加载
        this.isExtractingAPIKey = true;
        console.log('[API密钥提取Pro] 已禁用流量优化，确保页面正常加载');

        if (!this.page) {
            logger.error('Browser not initialized');
            this.isExtractingAPIKey = false; // 重置标志位
            return null;
        }

        // Initialize PageHelper if needed
        if (!this.helper) {
            logger.warn('PageHelper not initialized, creating new instance');
            this.helper = new PageHelper(this.page);
        }

        try {
            logger.info('Extracting API key with new OpenAI interface');
            // 等待Create organization文字的出现
            const createOrgClicked = await this.helper.clickByVisibleText('Create organization');

            if (!createOrgClicked) {
                logger.error('Could not click "Create organization" button');
                this.isExtractingAPIKey = false; // 重置标志位
                return null;
            }

            logger.info('Create organization Clicked.....Waiting for the page to load');

            // 等待Continue按钮的出现
            const continueBtnVisible = await this.helper.visibleTextExists('Continue','*',10000);

            if (!continueBtnVisible) {
                logger.error('Continue button not visible');
                this.isExtractingAPIKey = false; // 重置标志位
                return null;
            }

            const continueClicked = await this.helper.clickByVisibleText('Continue');

            if (!continueClicked) {
                logger.error('Could not click "Continue" button');
                this.isExtractingAPIKey = false; // 重置标志位
                return null;
            }

            logger.info('Continue Clicked.....Waiting for the page to load');

            let generateAPIKeyClicked = await this.helper.clickByVisibleText('Generate API Key');

            if (!generateAPIKeyClicked) {
                logger.error('Could not click "Generate API Key" button');
                this.isExtractingAPIKey = false; // 重置标志位
                return null;
            }

            logger.info('Generate API Key Clicked.....Waiting for the page to load');
            // 等待API密钥输入框的出现
            const apiKeyInputVisible = await this.helper.waitForElement('input[type="text"]', 5000);
            if (!apiKeyInputVisible) {
                logger.error('API key input field not found');
                this.isExtractingAPIKey = false; // 重置标志位
                return null;
            }
            // Extract the API key from the input field
            const apiKey = await this.helper.evaluate(() => {
                const input = document.querySelector('input[type="text"]') as HTMLInputElement | null;
                return input ? input.value : null;
            });
            if (!apiKey) {
                logger.error('API key value is empty');
                this.isExtractingAPIKey = false; // 重置标志位
                return null;
            }

            logger.info('API key extracted successfully');
            this.isExtractingAPIKey = false; // 重置标志位
            return apiKey;
        } catch (error) {
            logger.error('Error extracting API key:', error);
            // 只在启用截图保存时才截图
            if (config.get('debug.saveScreenshots', true)) {
                await this.takeScreenshot('api-key-extraction-error.png');
            }
            this.isExtractingAPIKey = false; // 重置标志位
            return null;
        }
    }

    /**
     * Close browser and clean up resources
     */
    async close(): Promise<void> {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
            this.page = null;
            logger.info('Browser closed');
        }
    }

    /**
     * Take screenshot for debugging
     */
    async takeScreenshot(path: string): Promise<boolean> {
        if (!this.page) {
            logger.error('Browser not initialized');
            return false;
        }

        // 如果 helper 为 null，创建一个新的 PageHelper 实例
        if (!this.helper) {
            this.helper = new PageHelper(this.page);
        }

        try {
            // 使用页面助手的截图功能
            return await this.helper.screenshot(path, {fullPage: true});
        } catch (error) {
            logger.error('Failed to take screenshot:', error);
            return false;
        }
    }
}
