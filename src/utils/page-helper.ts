import {Page} from 'puppeteer';
import {logger} from './logger';
import config from '../config';

/**
 * 页面交互助手
 * 提供各种查找、点击、输入元素的方法
 */
export class PageHelper {
    private page: Page;
    private defaultTimeout: number;

    constructor(page: Page, defaultTimeout: number = 30000) {
        this.page = page;
        this.defaultTimeout = defaultTimeout;
    }

    /**
     * 获取当前页面上所有按钮的文本内容
     * 用于调试目的，了解页面上有哪些可点击的按钮
     */
    async getAllButtonTexts(): Promise<string[]> {
        try {
            return await this.page.evaluate(() => {
                const buttons = Array.from(document.querySelectorAll('button'));
                return buttons.map(button => {
                    // 获取按钮的文本内容和类名，帮助识别
                    const text = button.textContent?.trim() || '';
                    const className = button.className;
                    const type = button.getAttribute('type') || '';
                    return `[${text}] (类型:${type}, 类名:${className})`;
                }).filter(text => text.length > 0);
            });
        } catch (error) {
            logger.error(`获取按钮文本失败: ${error}`);
            return [];
        }
    }

    /**
     * 等待页面出现指定文本后点击特定按钮
     * @param targetText 要监视的目标文本
     * @param buttonSelector 要点击的按钮选择器
     * @param checkInterval 检查间隔，默认500毫秒
     * @param timeout 超时时间，默认30秒
     * @returns 是否成功点击
     */
    async waitForTextThenClick(targetText: string, buttonSelector: string, checkInterval: number = 500, timeout: number = this.defaultTimeout): Promise<boolean> {
        try {
            const startTime = Date.now();

            while (Date.now() - startTime < timeout) {
                // 检查文本是否存在
                const textExists = await this.textExists(targetText, '*', 100);

                if (textExists) {
                    // 文本存在，点击按钮
                    logger.info(`检测到目标文本: "${targetText}"，准备点击按钮`);
                    return await this.click(buttonSelector);
                }

                // 等待指定间隔后再次检查
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }

            logger.warn(`等待文本"${targetText}"超时，未能点击按钮`);
            return false;
        } catch (error) {
            logger.error(`等待文本后点击失败: ${error}`);
            return false;
        }
    }

    /**
     * 等待页面出现指定元素后点击另一个按钮
     * @param targetSelector 要监视的目标元素选择器
     * @param buttonSelector 要点击的按钮选择器
     * @param checkInterval 检查间隔，默认500毫秒
     * @param timeout 超时时间，默认30秒
     * @returns 是否成功点击
     */
    async waitForElementThenClick(targetSelector: string, buttonSelector: string, checkInterval: number = 500, timeout: number = this.defaultTimeout): Promise<boolean> {
        try {
            const startTime = Date.now();

            while (Date.now() - startTime < timeout) {
                // 检查元素是否存在
                const elementExists = await this.exists(targetSelector, 100);

                if (elementExists) {
                    // 元素存在，点击按钮
                    logger.info(`检测到目标元素: "${targetSelector}"，准备点击按钮`);
                    return await this.click(buttonSelector);
                }

                // 等待指定间隔后再次检查
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }

            logger.warn(`等待元素"${targetSelector}"超时，未能点击按钮`);
            return false;
        } catch (error) {
            logger.error(`等待元素后点击失败: ${error}`);
            return false;
        }
    }

    /**
     * 按下回车键提交表单
     */
    async pressEnter(): Promise<boolean> {
        try {
            await this.page.keyboard.press('Enter');
            return true;
        } catch (error) {
            logger.error(`按回车键失败: ${error}`);
            return false;
        }
    }

    /**
     * 等待XPath元素出现
     */
    async waitForXPath(xpath: string, timeout: number = this.defaultTimeout): Promise<boolean> {
        try {
            // 使用evaluate实现等待XPath元素出现的功能
            await this.page.waitForFunction(
                (evalXpath) => {
                    return document.evaluate(
                        evalXpath,
                        document,
                        null,
                        XPathResult.FIRST_ORDERED_NODE_TYPE,
                        null
                    ).singleNodeValue !== null;
                },
                {timeout},
                xpath
            );
            return true;
        } catch (error) {
            logger.warn(`等待XPath元素超时: ${xpath}, 错误: ${error}`);
            return false;
        }
    }

    /**
     * 等待元素出现
     */
    async waitForElement(selector: string, timeout: number = this.defaultTimeout): Promise<boolean> {
        try {
            await this.page.waitForSelector(selector, {timeout});
            return true;
        } catch (error) {
            logger.warn(`等待元素超时: ${selector}, 错误: ${error}`);
            return false;
        }
    }

    /**
     * 点击元素
     */
    async click(selector: string, timeout: number = this.defaultTimeout): Promise<boolean> {
        try {
            if (await this.waitForElement(selector, timeout)) {
                await this.page.click(selector);
                return true;
            }
            return false;
        } catch (error) {
            logger.error(`点击元素失败: ${selector}, 错误: ${error}`);
            return false;
        }
    }

    /**
     * 通过XPath查找并点击元素
     */
    async clickByXPath(xpath: string, timeout: number = this.defaultTimeout): Promise<boolean> {
        try {
            // 使用waitForSelector替代waitForXPath，使用XPath选择器
            await this.page.waitForSelector(`xpath/${xpath}`, {timeout});

            // 使用$eval代替$x
            return await this.page.evaluate((evalXpath) => {
                const element = document.evaluate(
                    evalXpath,
                    document,
                    null,
                    XPathResult.FIRST_ORDERED_NODE_TYPE,
                    null
                ).singleNodeValue;

                if (element) {
                    (element as HTMLElement).click();
                    return true;
                }
                return false;
            }, xpath);
        } catch (error) {
            logger.error(`XPath点击失败: ${xpath}, 错误: ${error}`);
            return false;
        }
    }


    /**
     * 通过包含的文本查找并点击元素
     */
    async clickByText(text: string, elementType: string = '*', timeout: number = this.defaultTimeout): Promise<boolean> {
        // 使用XPath，查找包含指定文本的元素
        const xpath = `//${elementType}[contains(text(), "${text}")]`;
        return this.clickByXPath(xpath, timeout);
    }

    /**
     * 通过精确匹配文本查找并点击元素
     */
    async clickByExactText(text: string, elementType: string = '*', timeout: number = this.defaultTimeout): Promise<boolean> {
        // 使用XPath，查找文本完全匹配的元素
        const xpath = `//${elementType}[text()="${text}"]`;
        return this.clickByXPath(xpath, timeout);
    }

    /**
     * 使用evaluate直接执行JavaScript点击包含文本的元素
     */
    async clickByTextWithEval(text: string, elementType: string = 'button', timeout: number = this.defaultTimeout): Promise<boolean> {
        try {
            // 创建XPath表达式
            const xpath = `//${elementType}[contains(text(), "${text}")]`;

            // 等待元素出现
            if (!await this.waitForXPath(xpath, timeout)) {
                return false;
            }

            // 使用evaluate执行点击
            return await this.page.evaluate((evalText, evalElementType) => {
                const button = document.evaluate(
                    `//${evalElementType}[contains(text(), "${evalText}")]`,
                    document,
                    null,
                    XPathResult.FIRST_ORDERED_NODE_TYPE,
                    null
                ).singleNodeValue;

                if (button) {
                    (button as HTMLElement).click();
                    return true;
                }
                return false;
            }, text, elementType);
        } catch (error) {
            logger.error(`Evaluate点击失败: ${text}, 错误: ${error}`);
            return false;
        }
    }

    /**
     * 输入文本
     */
    async type(selector: string, text: string, options: { delay?: number } = {}): Promise<boolean> {
        try {
            if (await this.waitForElement(selector)) {
                await this.page.type(selector, text, options);
                return true;
            }
            return false;
        } catch (error) {
            logger.error(`输入文本失败: ${selector}, 错误: ${error}`);
            return false;
        }
    }

    /**
     * 获取元素文本
     */
    async getText(selector: string): Promise<string | null> {
        try {
            if (await this.waitForElement(selector)) {
                return await this.page.$eval(selector, el => el.textContent?.trim() || '');
            }
            return null;
        } catch (error) {
            logger.error(`获取文本失败: ${selector}, 错误: ${error}`);
            return null;
        }
    }

    /**
     * 获取元素属性
     */
    async getAttribute(selector: string, attributeName: string): Promise<string | null> {
        try {
            if (await this.waitForElement(selector)) {
                return await this.page.$eval(
                    selector,
                    (el, attr) => el.getAttribute(attr) || '',
                    attributeName
                );
            }
            return null;
        } catch (error) {
            logger.error(`获取属性失败: ${selector}, 属性: ${attributeName}, 错误: ${error}`);
            return null;
        }
    }

    /**
     * 检查元素是否存在
     */
    async exists(selector: string, timeout: number = 5000): Promise<boolean> {
        try {
            await this.page.waitForSelector(selector, {timeout});
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 检查包含指定文本的元素是否存在
     */
    async textExists(text: string, elementType: string = '*', timeout: number = 5000): Promise<boolean> {
        try {
            // 使用 waitForFunction 等待包含指定文本的元素出现
            await this.page.waitForFunction(
                (searchText, tagName) => {
                    // 使用 XPath 在页面中查找包含指定文本的元素
                    const xpath = `//${tagName}[contains(text(), "${searchText}")]`;
                    return document.evaluate(
                        xpath,
                        document,
                        null,
                        XPathResult.FIRST_ORDERED_NODE_TYPE,
                        null
                    ).singleNodeValue !== null;
                },
                {timeout},
                text,
                elementType
            );
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 等待导航完成
     */
    async waitForNavigation(options: {
        timeout?: number,
        waitUntil?: 'load' | 'domcontentloaded' | 'networkidle0' | 'networkidle2'
    } = {}): Promise<boolean> {
        try {
            await this.page.waitForNavigation({
                timeout: options.timeout || this.defaultTimeout,
                waitUntil: options.waitUntil || 'networkidle2'
            });
            return true;
        } catch (error) {
            logger.error(`等待导航超时: ${error}`);
            return false;
        }
    }

    /**
     * 截取截图
     */
    async screenshot(path: string, options: { fullPage?: boolean } = {}): Promise<boolean> {
        // 检查是否启用截图保存
        if (!config.get('debug.saveScreenshots', true)) {
            logger.debug('截图保存已禁用，跳过截图');
            return true; // 返回true避免影响业务流程
        }

        try {
            await this.page.screenshot({
                path,
                fullPage: options.fullPage || false
            });
            logger.info(`截图已保存: ${path}`);
            return true;
        } catch (error) {
            logger.error(`截图失败: ${error}`);
            return false;
        }
    }

    /**
     * 执行自定义JavaScript
     */
    async evaluate<T>(fn: (...args: any[]) => T, ...args: any[]): Promise<T> {
        return this.page.evaluate(fn, ...args);
    }

    /**
     * 使用真实的鼠标模拟点击元素
     * @param selector 元素选择器
     * @param timeout 超时时间
     * @returns 是否成功点击
     */
    async clickWithMouse(selector: string, timeout: number = this.defaultTimeout): Promise<boolean> {
        try {
            if (await this.waitForElement(selector, timeout)) {
                const element = await this.page.$(selector);
                if (element) {
                    const box = await element.boundingBox();
                    if (box) {
                        // 计算元素中心点
                        const x = box.x + box.width / 2;
                        const y = box.y + box.height / 2;

                        // 模拟真实的鼠标移动和点击
                        await this.page.mouse.move(x, y);
                        await new Promise(resolve => setTimeout(resolve, 100));
                        await this.page.mouse.down();
                        await new Promise(resolve => setTimeout(resolve, 50));
                        await this.page.mouse.up();

                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            logger.error(`鼠标模拟点击失败: ${selector}, 错误: ${error}`);
            return false;
        }
    }

    /**
     * 通过文本查找并使用鼠标模拟点击元素
     * @param text 要查找的文本
     * @param elementType 元素类型
     * @param timeout 超时时间
     * @returns 是否成功点击
     */
    async clickByTextWithMouse(text: string, elementType: string = '*', timeout: number = this.defaultTimeout): Promise<boolean> {
        try {
            // 使用正确的CSS选择器
            const selector = `${elementType}`;
            const elements = await this.page.$$(selector);

            // 遍历所有元素，找到包含目标文本的元素
            for (const element of elements) {
                const elementText = await element.evaluate(el => el.textContent || '');
                if (elementText.includes(text)) {
                    const box = await element.boundingBox();
                    if (box) {
                        const x = box.x + box.width / 2;
                        const y = box.y + box.height / 2;

                        await this.page.mouse.move(x, y);
                        await new Promise(resolve => setTimeout(resolve, 100));
                        await this.page.mouse.down();
                        await new Promise(resolve => setTimeout(resolve, 50));
                        await this.page.mouse.up();

                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            logger.error(`鼠标模拟点击文本失败: ${text}, 错误: ${error}`);
            return false;
        }
    }

    /**
     * 监控页面错误并自动重试
     * @param errorText 错误文本
     * @param retryButtonText 重试按钮文本
     * @param checkInterval 检查间隔，默认500毫秒
     * @param timeout 超时时间，默认30秒
     */
    async monitorErrorAndRetry(
        errorText: string = "Oops, an error occurred!",
        retryButtonText: string = "Try again",
        checkInterval: number = 500,
        timeout: number = this.defaultTimeout
    ): Promise<void> {
        try {
            const startTime = Date.now();

            while (Date.now() - startTime < timeout) {
                // 检查错误文本是否存在
                const errorExists = await this.textExists(errorText, '*', 100);

                if (errorExists) {
                    logger.warn(`检测到错误: "${errorText}"，尝试点击重试按钮`);

                    // 尝试点击重试按钮
                    const retrySuccess = await this.clickByText(retryButtonText, "button");
                    if (retrySuccess) {
                        logger.info('成功点击重试按钮');
                        // 等待页面响应
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }

                // 等待指定间隔后再次检查
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }
        } catch (error) {
            logger.error(`错误监控失败: ${error}`);
        }
    }

    /**
     * 监控特定元素出现并立即点击
     * @param targetSelector 要监控的元素选择器
     * @param maxDuration 最大监控时长(毫秒)，默认5分钟；设置为0或负值表示无限监控
     * @param checkInterval 检查间隔(毫秒)，默认100ms
     * @returns 是否成功点击
     */
    async monitorAndClickWhenVisible(
        targetSelector: string,
        maxDuration: number = 5 * 60 * 1000,
        checkInterval: number = 100
    ): Promise<boolean> {
        try {
            const startTime = Date.now();
            let clicked = false;
            const isInfiniteMonitoring = maxDuration <= 0;

            logger.info(`开始监控元素: ${targetSelector}${isInfiniteMonitoring ? '（无限时长）' : ''}`);

            // 创建监控循环
            while ((isInfiniteMonitoring || Date.now() - startTime < maxDuration) && !clicked) {
                // 检查元素是否存在 (使用较短的超时以便快速检查)
                const exists = await this.exists(targetSelector, 50);

                if (exists) {
                    logger.info(`元素出现: ${targetSelector}，立即点击`);
                    // 元素存在，立即点击
                    clicked = await this.click(targetSelector);

                    if (clicked) {
                        logger.info(`成功点击元素: ${targetSelector}`);
                        return true;
                    } else {
                        logger.warn(`点击元素失败: ${targetSelector}，将继续监控`);
                    }
                }

                // 短暂等待后再次检查
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }

            if (!clicked) {
                logger.warn(`监控超时，未能点击元素: ${targetSelector}`);
            }

            return clicked;
        } catch (error) {
            logger.error(`监控元素并点击失败: ${targetSelector}, 错误: ${error}`);
            return false;
        }
    }

    /**
     * 监控包含特定文本的元素出现并立即点击
     * @param targetText 要监控的文本内容
     * @param elementType 元素类型，默认为所有元素
     * @param maxDuration 最大监控时长(毫秒)，默认5分钟；设置为0或负值表示无限监控
     * @param checkInterval 检查间隔(毫秒)，默认100ms
     * @returns 是否成功点击
     */
    async monitorTextAndClickWhenVisible(
        targetText: string,
        elementType: string = '*',
        maxDuration: number = 5 * 60 * 1000,
        checkInterval: number = 100
    ): Promise<boolean> {
        try {
            const startTime = Date.now();
            let clicked = false;
            const isInfiniteMonitoring = maxDuration <= 0;

            logger.info(`开始监控文本: "${targetText}"${isInfiniteMonitoring ? '（无限时长）' : ''}`);

            // 创建监控循环
            while ((isInfiniteMonitoring || Date.now() - startTime < maxDuration) && !clicked) {
                // 检查文本是否存在 (使用较短的超时以便快速检查)
                const exists = await this.textExists(targetText, elementType, 50);

                if (exists) {
                    logger.info(`检测到文本: "${targetText}"，立即点击`);
                    // 文本存在，立即点击
                    clicked = await this.clickByText(targetText, elementType);

                    if (clicked) {
                        logger.info(`成功点击包含文本的元素: "${targetText}"`);
                        return true;
                    } else {
                        logger.warn(`点击包含文本的元素失败: "${targetText}"，将继续监控`);
                    }
                }

                // 短暂等待后再次检查
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }

            if (!clicked) {
                logger.warn(`监控超时，未能点击包含文本的元素: "${targetText}"`);
            }

            return clicked;
        } catch (error) {
            logger.error(`监控文本并点击失败: "${targetText}", 错误: ${error}`);
            return false;
        }
    }

    /**
     * 通过包含的文本查找并点击可见的元素
     */
    async clickByVisibleText(text: string, elementType: string = '*', timeout: number = this.defaultTimeout): Promise<boolean> {
        // 使用XPath，查找包含指定文本的可见元素
        const xpath = `//${elementType}[contains(text(), "${text}") and not(ancestor::div[@style="display:none"])]`;
        return this.clickByXPath(xpath, timeout);
    }

    /**
     * 检查页面中可见的、包含指定文本的元素是否存在
     * @param text 要查找的文本
     * @param elementType 元素类型，默认为所有元素（'*'）
     * @param timeout 超时时间，默认为5000毫秒
     * @returns 如果找到可见的包含指定文本的元素，则返回true；否则返回false
     */
    async visibleTextExists(text: string, elementType: string = '*', timeout: number = 5000): Promise<boolean> {
        try {
            // waitForFunction 会持续轮询直到条件满足或超时
            await this.page.waitForFunction(
                (searchText, tagName) => {
                    // 修复1：正确处理文本中的引号
                    const escapedText = searchText.replace(/"/g, '\\"');

                    // 修复2：同时查找直接包含文本的元素和通过子节点包含文本的元素
                    const directXpath = `//${tagName}[text()[contains(., "${escapedText}")]]`;
                    const containsXpath = `//${tagName}[contains(., "${escapedText}")]`;

                    // 使用两种XPath查询
                    const xpaths = [directXpath, containsXpath];

                    for (const xpath of xpaths) {
                        const elements = document.evaluate(
                            xpath,
                            document,
                            null,
                            XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
                            null
                        );

                        // 检查找到的每个元素是否可见
                        for (let i = 0; i < elements.snapshotLength; i++) {
                            const element = elements.snapshotItem(i) as HTMLElement;

                            // 修复3：使用更可靠的可见性检查
                            if (isElementVisible(element)) {
                                return true; // 找到一个可见的元素，返回true
                            }
                        }
                    }

                    // 辅助函数：检查元素是否真正可见
                    function isElementVisible(element: HTMLElement): boolean {
                        if (!element) return false;

                        // 检查元素自身是否可见
                        const rect = element.getBoundingClientRect();
                        const style = window.getComputedStyle(element);

                        // 元素必须有尺寸
                        if (rect.width === 0 || rect.height === 0) return false;

                        // 元素必须在视口内
                        if (rect.bottom < 0 || rect.top > window.innerHeight ||
                            rect.right < 0 || rect.left > window.innerWidth) return false;

                        // 元素必须没有被隐藏
                        if (style.display === 'none' || style.visibility === 'hidden' ||
                            style.opacity === '0') return false;

                        // 检查所有父元素
                        let parent = element.parentElement;
                        while (parent) {
                            const parentStyle = window.getComputedStyle(parent);
                            if (parentStyle.display === 'none' ||
                                parentStyle.visibility === 'hidden') {
                                return false;
                            }
                            parent = parent.parentElement;
                        }

                        return true;
                    }

                    return false; // 没有找到可见的元素，继续轮询
                },
                {
                    timeout,
                    polling: 'raf' // 使用 requestAnimationFrame 进行平滑轮询
                },
                text,
                elementType
            );

            return true; // 找到了元素
        } catch (error) {
            // 超时或其他错误
            return false;
        }
    }

// 额外提供一个更灵活的版本，支持更多选项
    async waitForVisibleText(
        text: string,
        options: {
            elementType?: string;
            timeout?: number;
            exact?: boolean; // 是否精确匹配
            caseSensitive?: boolean; // 是否区分大小写
        } = {}
    ): Promise<boolean> {
        const {
            elementType = '*',
            timeout = 5000,
            exact = false,
            caseSensitive = true
        } = options;

        try {
            await this.page.waitForFunction(
                (searchText, tagName, exactMatch, caseSensitive) => {
                    // 处理大小写
                    const processText = (str: string) => caseSensitive ? str : str.toLowerCase();
                    const targetText = processText(searchText);

                    // 获取所有指定类型的元素
                    const elements = document.querySelectorAll(tagName);

                    for (const element of Array.from(elements)) {
                        // 获取元素的文本内容
                        const elementText = processText(element.textContent || '');

                        // 根据匹配模式检查文本
                        const isMatch = exactMatch
                            ? elementText.trim() === targetText.trim()
                            : elementText.includes(targetText);

                        if (isMatch && isElementVisible(element as HTMLElement)) {
                            return true;
                        }
                    }

                    // 辅助函数（同上）
                    function isElementVisible(element: HTMLElement): boolean {
                        if (!element) return false;

                        const rect = element.getBoundingClientRect();
                        const style = window.getComputedStyle(element);

                        if (rect.width === 0 || rect.height === 0) return false;
                        if (rect.bottom < 0 || rect.top > window.innerHeight ||
                            rect.right < 0 || rect.left > window.innerWidth) return false;
                        if (style.display === 'none' || style.visibility === 'hidden' ||
                            style.opacity === '0') return false;

                        let parent = element.parentElement;
                        while (parent) {
                            const parentStyle = window.getComputedStyle(parent);
                            if (parentStyle.display === 'none' ||
                                parentStyle.visibility === 'hidden') {
                                return false;
                            }
                            parent = parent.parentElement;
                        }

                        return true;
                    }

                    return false;
                },
                { timeout, polling: 'raf' },
                text,
                elementType,
                exact,
                caseSensitive
            );

            return true;
        } catch {
            return false;
        }
    }

}
