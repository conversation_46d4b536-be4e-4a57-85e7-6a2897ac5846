import {logger} from './logger';
import * as fs from 'fs';
import * as path from 'path';
import config from '../config';

/**
 * 邮件工具类
 * 用于保存和管理邮件内容
 */
export class EmailHelper {
    private static instance: EmailHelper;
    private emailsDir: string;

    private constructor() {
        // 创建emails目录（如果不存在）
        this.emailsDir = path.join(process.cwd(), 'emails');
        if (!fs.existsSync(this.emailsDir)) {
            fs.mkdirSync(this.emailsDir);
        }
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): EmailHelper {
        if (!EmailHelper.instance) {
            EmailHelper.instance = new EmailHelper();
        }
        return EmailHelper.instance;
    }

    /**
     * 保存邮件内容到文件
     * @param emailContent 邮件内容
     * @param type 邮件类型（如：verification, notification等）
     * @param source 邮件来源（如：page, api等）
     * @param additionalInfo 附加信息（可选）
     */
    public async saveEmail(
        emailContent: string,
        type: string,
        source: string,
        additionalInfo?: Record<string, any>
    ): Promise<void> {
        // 检查是否启用邮件保存
        if (!config.get('debug.saveEmails', true)) {
            logger.debug('邮件保存已禁用，跳过保存邮件内容');
            return;
        }

        try {
            // 生成文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `email-${type}-${source}-${timestamp}.txt`;
            const filePath = path.join(this.emailsDir, filename);

            // 准备保存的内容
            const content = {
                timestamp: new Date().toISOString(),
                type,
                source,
                content: emailContent,
                ...additionalInfo
            };

            // 保存文件
            fs.writeFileSync(filePath, JSON.stringify(content, null, 2));
            logger.info(`邮件内容已保存到: ${filePath}`);
        } catch (error) {
            logger.error(`保存邮件内容失败: ${error}`);
        }
    }

    /**
     * 保存验证码邮件
     * @param emailContent 邮件内容
     * @param source 邮件来源
     * @param code 验证码（可选）
     */
    public async saveVerificationEmail(
        emailContent: string,
        source: string,
        code?: string
    ): Promise<void> {
        await this.saveEmail(emailContent, 'verification', source, {code});
    }

    /**
     * 获取所有保存的邮件
     * @param type 邮件类型（可选）
     * @param source 邮件来源（可选）
     */
    public async getSavedEmails(type?: string, source?: string): Promise<any[]> {
        try {
            const files = fs.readdirSync(this.emailsDir);
            const emails = [];

            for (const file of files) {
                if (file.endsWith('.txt')) {
                    const filePath = path.join(this.emailsDir, file);
                    const content = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

                    // 如果指定了类型或来源，进行过滤
                    if ((!type || content.type === type) && 
                        (!source || content.source === source)) {
                        emails.push(content);
                    }
                }
            }

            return emails;
        } catch (error) {
            logger.error(`获取保存的邮件失败: ${error}`);
            return [];
        }
    }
} 