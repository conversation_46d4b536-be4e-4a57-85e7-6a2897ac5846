/**
 * 处理 EMAIL_EASY_CONFIG 环境变量
 * 格式: email----password----token----uuid
 */

import {logger} from './logger';

/**
 * 解析 EMAIL_EASY_CONFIG 环境变量并设置相关配置
 * @returns 是否成功加载配置
 */
export function loadEasyConfig(): boolean {
    try {
        const easyConfig = process.env.EMAIL_EASY_CONFIG;
        
        if (!easyConfig) {
            return false;
        }
        
        logger.info('Found EMAIL_EASY_CONFIG environment variable');
        
        // 解析格式: email----password----token----uuid
        const parts = easyConfig.split('----');
        if (parts.length >= 3) {
            const email = parts[0].trim();
            const password = parts[1].trim();
            const token = parts[2].trim();
            
            // 覆盖环境变量
            process.env.EMAIL_ADDRESS = email;
            process.env.EMAIL_PASSWORD = password;
            process.env.REFRESH_TOKEN = token;
            
            // 如果有第四部分，使用它作为CLIENT_ID，否则使用默认值
            if (parts.length >= 4) {
                process.env.CLIENT_ID = parts[3].trim();
            } else {
                // 如果没有提供CLIENT_ID，使用一个默认值
                process.env.CLIENT_ID = '9e5f94bc-e8a4-4e73-b8be-63364c29d753';
            }
            
            // 设置其他可能需要的环境变量
            if (!process.env.CLIENT_SECRET) {
                process.env.CLIENT_SECRET = '';  // 微软邮箱通常不需要CLIENT_SECRET
            }
            
            logger.info(`Successfully loaded account info from EMAIL_EASY_CONFIG`);
            logger.info(`Email: ${process.env.EMAIL_ADDRESS}`);
            logger.info(`Using provider: microsoft`);
            console.log(`============简易邮箱解析==========`);
            console.log(`邮箱: ${process.env.EMAIL_ADDRESS}`);
            console.log(`密码: ${process.env.EMAIL_PASSWORD}`);
            
            
            return true;
        } else {
            logger.warn('Warning: EMAIL_EASY_CONFIG format is invalid. Expected format: email----password----token----uuid');
            return false;
        }
    } catch (error) {
        logger.error(`Error processing EMAIL_EASY_CONFIG:`, error);
        return false;
    }
}
