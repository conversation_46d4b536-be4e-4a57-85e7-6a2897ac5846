import * as fs from 'fs';
import * as path from 'path';
import { logger } from './logger';

export interface AccountInfo {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
}

export class EmailGenerator {
    private domain: string;
    private names: string[];
    private defaultPassword: string;
    private defaultFirstName: string;
    private defaultLastName: string;

    constructor(
        domain: string,
        password?: string,
        firstName?: string,
        lastName?: string
    ) {
        this.domain = domain;
        this.names = this.loadNames();
        this.defaultPassword = password || this.generateRandomPassword();
        this.defaultFirstName = firstName || this.generateRandomName();
        this.defaultLastName = lastName || this.generateRandomName();
    }

    private loadNames(): string[] {
        try {
            const namesPath = path.join(__dirname, '../../names-dataset.txt');
            const namesContent = fs.readFileSync(namesPath, 'utf-8');
            return namesContent.split('\n').filter(name => name.trim() !== '');
        } catch (error) {
            logger.error('Error loading names dataset:', error);
            // 如果文件不存在，返回一些默认名字
            return ['John', '<PERSON>', '<PERSON>', 'Sarah', 'David', '<PERSON>'];
        }
    }

    private generateRandomPassword(length: number = 12): string {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        return Array.from(
            { length },
            () => chars[Math.floor(Math.random() * chars.length)]
        ).join('');
    }

    private generateRandomName(): string {
        return this.names[Math.floor(Math.random() * this.names.length)];
    }

    public generateEmail(length: number = 8): string {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        const randomString = Array.from(
            { length },
            () => chars[Math.floor(Math.random() * chars.length)]
        ).join('');
        const timestamp = Date.now().toString().slice(-4);
        return `${this.defaultFirstName.toLowerCase()}${randomString}${timestamp}@${this.domain}`;
    }

    public getAccountInfo(): AccountInfo {
        return {
            email: this.generateEmail(),
            password: this.defaultPassword,
            firstName: this.defaultFirstName,
            lastName: this.defaultLastName,
        };
    }
} 