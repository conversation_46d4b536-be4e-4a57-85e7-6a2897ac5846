import crypto from 'crypto';

/**
 * Helper utilities for the OpenAI registration system
 */

/**
 * Generate a random password that meets OpenAI's requirements
 * - At least 8 characters
 * - Contains uppercase, lowercase, numbers, and special characters
 */
export function generatePassword(length: number = 16): string {
    const uppercaseChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ'; // removed I,O for clarity
    const lowercaseChars = 'abcdefghijkmnopqrstuvwxyz'; // removed l for clarity
    const numberChars = '23456789'; // removed 0,1 for clarity
    const specialChars = '!@#$%^&*()-_=+[]{}|;:,.<>?';

    const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;

    // Ensure password has at least one of each character type
    let password = '';
    password += uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length));
    password += lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length));
    password += numberChars.charAt(Math.floor(Math.random() * numberChars.length));
    password += specialChars.charAt(Math.floor(Math.random() * specialChars.length));

    // Fill the rest with random characters
    for (let i = password.length; i < length; i++) {
        password += allChars.charAt(Math.floor(Math.random() * allChars.length));
    }

    // Shuffle the password
    return password.split('').sort(() => 0.5 - Math.random()).join('');
}

/**
 * Generate a random date of birth (21-60 years old)
 */
export function generateDateOfBirth(): { day: string; month: string; year: string } {
    const now = new Date();
    const minAge = 21;
    const maxAge = 60;

    const minYear = now.getFullYear() - maxAge;
    const maxYear = now.getFullYear() - minAge;
    const year = Math.floor(Math.random() * (maxYear - minYear + 1)) + minYear;

    const month = Math.floor(Math.random() * 12) + 1;
    let day = Math.floor(Math.random() * 28) + 1; // Using 28 to be safe with February

    return {
        day: day.toString(),
        month: month.toString(),
        year: year.toString()
    };
}

/**
 * Generate random user data for registration
 */
export function generateUserData(country: string = 'US'): {
    firstName: string;
    lastName: string;
    dateOfBirth: { day: string; month: string; year: string };
    country: string;
} {
    // List of common first names
    const firstNames = [
        'James', 'John', 'Robert', 'Michael', 'William', 'David', 'Richard', 'Joseph',
        'Thomas', 'Charles', 'Mary', 'Patricia', 'Jennifer', 'Linda', 'Elizabeth',
        'Barbara', 'Susan', 'Jessica', 'Sarah', 'Karen'
    ];

    // List of common last names
    const lastNames = [
        'Smith', 'Johnson', 'Williams', 'Jones', 'Brown', 'Davis', 'Miller', 'Wilson',
        'Moore', 'Taylor', 'Anderson', 'Thomas', 'Jackson', 'White', 'Harris', 'Martin',
        'Thompson', 'Garcia', 'Martinez', 'Robinson'
    ];

    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    const dateOfBirth = generateDateOfBirth();

    return {
        firstName,
        lastName,
        dateOfBirth,
        country
    };
}

/**
 * Sleep for a specified duration
 */
export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Generate a random email address for testing
 * Note: This is for development/testing only
 */
export function generateTestEmail(domain: string = 'example.com'): string {
    const randomString = crypto.randomBytes(8).toString('hex');
    return `test_${randomString}@${domain}`;
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Extract domain from email
 */
export function extractDomain(email: string): string {
    const parts = email.split('@');
    return parts.length === 2 ? parts[1] : '';
}

/**
 * Create a delay with random jitter to appear more human-like
 */
export function humanDelay(baseMs: number, jitterMs: number = 500): Promise<void> {
    const jitter = Math.floor(Math.random() * jitterMs);
    return sleep(baseMs + jitter);
}
