# 代理配置指南

为了避免IP被限制，OpenAI RegFlow现在支持通过代理服务器进行访问。

## 支持的代理类型

- **HTTP代理**: `http://proxy-server:port`
- **HTTPS代理**: `https://proxy-server:port`  
- **SOCKS5代理**: `socks5://proxy-server:port`

## 环境变量配置

### 基础配置

```bash
# 启用代理
PROXY_ENABLED=true

# 代理服务器地址
PROXY_SERVER=http://your-proxy-server:8080

# 代理类型 (http, https, socks5)
PROXY_TYPE=http
```

### 代理认证配置（如果代理需要用户名密码）

```bash
# 代理用户名
PROXY_USERNAME=your_username

# 代理密码  
PROXY_PASSWORD=your_password
```

## Docker Compose配置示例

### 单容器配置

在 `docker-compose.yml` 中添加环境变量：

```yaml
services:
  openai-regflow:
    # ... 其他配置
    environment:
      - NODE_ENV=production
      - PROXY_ENABLED=true
      - PROXY_SERVER=http://proxy-server:8080
      - PROXY_TYPE=http
      - PROXY_USERNAME=username  # 可选
      - PROXY_PASSWORD=password  # 可选
```

### Mac版本配置

在Mac系统上使用 `docker-compose.mac.yml` 或通过 `start_many_mac.sh` 脚本时：

```yaml
services:
  openai-regflow-example:
    # ... 其他Mac优化配置
    environment:
      # Mac优化环境变量
      - NODE_ENV=production
      - NODE_OPTIONS=--max-old-space-size=1024 --max-semi-space-size=128
      
      # 代理配置
      - PROXY_ENABLED=true
      - PROXY_SERVER=http://proxy-server:8080
      - PROXY_TYPE=http
      - PROXY_USERNAME=username  # 可选
      - PROXY_PASSWORD=password  # 可选
```

### 批量容器配置

使用 `start_many.sh`、`start_many_v1.sh` 或 `start_many_mac.sh` 脚本时，可以编辑脚本文件，取消代理配置行的注释并填入相应信息。

## 常见代理配置示例

### HTTP代理（无认证）
```bash
PROXY_ENABLED=true
PROXY_SERVER=http://127.0.0.1:8080
PROXY_TYPE=http
```

### HTTP代理（带认证）
```bash
PROXY_ENABLED=true
PROXY_SERVER=http://proxy.example.com:3128  
PROXY_TYPE=http
PROXY_USERNAME=myuser
PROXY_PASSWORD=mypass
```

### SOCKS5代理
```bash
PROXY_ENABLED=true
PROXY_SERVER=socks5://127.0.0.1:1080
PROXY_TYPE=socks5
PROXY_USERNAME=username  # 如果需要
PROXY_PASSWORD=password  # 如果需要
```

## 验证代理配置

启动容器后，查看日志确认代理配置是否生效：

```bash
docker logs your-container-name
```

成功配置代理后，你应该在日志中看到类似以下信息：
```
[浏览器] 使用代理: http://proxy-server:8080
启用代理配置: http://proxy-server:8080
设置代理认证，用户名: username
```

## 注意事项

1. **代理服务器要求**: 确保代理服务器稳定可用，支持HTTPS连接
2. **认证信息安全**: 代理用户名和密码将以明文形式存储在环境变量中，请注意安全
3. **网络延迟**: 使用代理可能增加网络延迟，建议选择高速代理服务器
4. **代理类型**: 根据你的代理服务器类型选择正确的PROXY_TYPE
5. **防火墙设置**: 确保容器可以访问代理服务器的端口
6. **Mac系统特殊配置**: 
   - Apple Silicon (M1/M2/M3) Mac需要确保代理服务器支持ARM64架构的连接
   - 使用 `start_many_mac.sh` 脚本时，代理配置会自动应用Mac优化参数
   - Docker Desktop for Mac需要允许代理服务器的网络访问权限

## 故障排除

### 代理连接失败
- 检查代理服务器地址和端口是否正确
- 确认代理服务器是否运行正常
- 验证网络连接是否畅通

### 认证失败
- 检查用户名和密码是否正确
- 确认代理服务器是否要求认证

### 性能问题
- 尝试使用不同的代理服务器
- 调整容器资源限制
- 监控代理服务器负载

### Mac系统特定问题
- **Docker Desktop连接问题**: 检查Docker Desktop的网络设置，确保允许容器访问外部网络
- **Apple Silicon兼容性**: 如果代理服务器不支持ARM64，尝试使用Rosetta模式运行
- **内存不足**: Mac系统内存不足时，减少同时运行的容器数量或调整每个容器的内存限制
- **代理服务器无响应**: 检查macOS防火墙设置，确保代理端口未被阻止
- **网络切换问题**: Mac在WiFi和有线网络间切换时，重启Docker Desktop和容器 