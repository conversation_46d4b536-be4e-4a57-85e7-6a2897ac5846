#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 自动检测并设置正确的 docker-compose 命令
detect_docker_compose() {
    # 优先检查 docker compose (v2)
    if docker compose version &> /dev/null; then
        echo "docker compose"
        return 0
    fi

    # 检查 docker-compose (v1)
    if command -v docker-compose &> /dev/null; then
        echo "docker-compose"
        return 0
    fi

    # 都没找到
    return 1
}

# 自动检测 Docker Compose
DOCKER_COMPOSE_CMD=$(detect_docker_compose)

if [ -z "$DOCKER_COMPOSE_CMD" ]; then
    echo -e "${RED}错误: 未找到 Docker Compose，请先安装 Docker Compose${NC}"
    echo -e "${YELLOW}安装方法:${NC}"
    echo -e "  对于 Docker Compose V2: 安装最新版 Docker Desktop 或运行 'docker compose' 插件"
    echo -e "  对于 Docker Compose V1: sudo apt-get install docker-compose (Ubuntu/Debian)"
    exit 1
fi

# 显示检测到的版本
echo -e "${GREEN}检测到 Docker Compose 命令: $DOCKER_COMPOSE_CMD${NC}"

# 检查是否需要重建镜像
check_rebuild_needed() {
    echo -e "\n${YELLOW}是否需要重建镜像？${NC}"
    echo -e "1) 否，继续使用现有镜像"
    echo -e "2) 是，重建镜像（环境变更或镜像更新）"
    read -p "请选择 (1/2): " REBUILD_CHOICE

    if [ "$REBUILD_CHOICE" == "2" ]; then
        return 0
    else
        return 1
    fi
}

# 重建镜像函数
rebuild_image() {
    echo -e "${YELLOW}正在删除旧镜像...${NC}"
    docker rmi openai-regflow -f

    echo -e "${YELLOW}正在构建新镜像...${NC}"
    docker build --no-cache -t openai-regflow .
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 镜像构建失败，请检查Docker配置和磁盘空间${NC}"
        exit 1
    fi
    echo -e "${GREEN}镜像重建成功!${NC}"
}

# 检查是否是更新模式
if [ "$1" == "update" ]; then
    echo -e "${BLUE}=========================================${NC}"
    echo -e "${GREEN}OpenAI-RegFlow 更新模式${NC}"
    echo -e "${BLUE}=========================================${NC}"

    # 检查compose文件是否存在
    MAIN_COMPOSE_FILE="container_info/docker-compose.yml"
    if [ ! -f "$MAIN_COMPOSE_FILE" ]; then
        echo -e "${RED}错误: 未找到 docker-compose.yml 文件${NC}"
        echo -e "${YELLOW}请先运行脚本创建容器${NC}"
        exit 1
    fi

    # 停止所有容器
    echo -e "${YELLOW}正在停止所有容器...${NC}"
    $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE down

    # 重建镜像
    rebuild_image

    # 重启所有容器
    echo -e "${YELLOW}正在重启所有容器...${NC}"
    $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d

    # 显示容器状态
    sleep 3
    echo -e "\n${BLUE}当前容器状态:${NC}"
    docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}"

    echo -e "\n${GREEN}更新完成！所有容器已使用新镜像重启${NC}"
    exit 0
fi

# 检查并构建镜像
if docker image inspect openai-regflow >/dev/null 2>&1; then
    echo -e "${GREEN}镜像 openai-regflow 已存在${NC}"
    if check_rebuild_needed; then
        rebuild_image
    fi
else
    echo -e "${YELLOW}正在构建基础镜像...${NC}"
    docker build -t openai-regflow .
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 镜像构建失败，请检查Docker配置和磁盘空间${NC}"
        exit 1
    fi
    echo -e "${GREEN}镜像构建成功!${NC}"
fi

# 创建container_info目录
mkdir -p container_info

# 创建必要的目录
mkdir -p container_info/logs container_info/screenshots container_info/emails

# 显示欢迎信息
echo -e "${BLUE}=========================================${NC}"
echo -e "${GREEN}OpenAI-RegFlow 多容器管理脚本${NC}"
echo -e "${BLUE}=========================================${NC}"

# 创建主compose文件
MAIN_COMPOSE_FILE="container_info/docker-compose.yml"

# 初始化主compose文件
cat > $MAIN_COMPOSE_FILE << EOF
version: '3.8'

services:
EOF

# 容器计数器
CONTAINER_COUNT=0

# 添加批量输入功能
echo -e "\n${YELLOW}选择输入方式:${NC}"
echo -e "1) 逐个输入账号信息"
echo -e "2) 从文件批量导入 (每行一个账号，格式: email----password----token----clientId)"
echo -e "3) 批量创建自建邮箱账号 (需指定域名和临时邮箱)"
read -p "请选择 (1/2/3): " INPUT_MODE

# 处理自建邮箱模式
if [ "$INPUT_MODE" == "3" ]; then
    # 询问要创建的容器数量
    read -p "请输入要创建的容器数量: " CONTAINER_TO_CREATE
    
    # 校验是否为数字
    if ! [[ "$CONTAINER_TO_CREATE" =~ ^[0-9]+$ ]]; then
        echo -e "${RED}错误: 请输入有效的数字${NC}"
        exit 1
    fi
    
    echo -e "\n${YELLOW}将依次创建 ${GREEN}$CONTAINER_TO_CREATE${NC} 个容器，每个容器需要输入域名和临时邮箱${NC}"
    echo -e "${YELLOW}输入格式: 域名----临时邮箱----其他信息 (最后一段将被忽略)${NC}"
    echo -e "${YELLOW}示例: <EMAIL>----107.175.63.168-RackNerd${NC}"
    read -p "确认继续? (y/n): " CONFIRM
    
    if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
        echo -e "${RED}操作已取消${NC}"
        exit 0
    fi
    
    # 逐个创建容器
    for (( i=0; i<$CONTAINER_TO_CREATE; i++ ))
    do
        echo -e "\n${YELLOW}正在配置第 $((i+1))/$CONTAINER_TO_CREATE 个自建邮箱容器${NC}"
        
        # 输入完整配置信息
        read -p "请输入配置信息 (格式: 域名----临时邮箱----其他): " CUSTOM_CONFIG
        
        # 验证输入格式
        if [[ ! $CUSTOM_CONFIG == *----* ]]; then
            echo -e "${RED}错误: 输入格式不正确，需要包含 ---- 分隔符，跳过此容器${NC}"
            continue
        fi
        
        # 拆分配置信息
        CUSTOM_DOMAIN=$(echo "$CUSTOM_CONFIG" | awk -F'----' '{print $1}')
        CUSTOM_TEMP_MAIL=$(echo "$CUSTOM_CONFIG" | awk -F'----' '{print $2}')
        
        if [ -z "$CUSTOM_DOMAIN" ] || [ -z "$CUSTOM_TEMP_MAIL" ]; then
            echo -e "${RED}错误: 域名和临时邮箱不能为空，跳过此容器${NC}"
            continue
        fi
        
        CONTAINER_NAME="openai-regflow-$CONTAINER_COUNT"
        
        # 创建容器专用目录
        mkdir -p container_info/logs$CONTAINER_COUNT container_info/screenshots$CONTAINER_COUNT container_info/emails$CONTAINER_COUNT
        
        # 将容器配置添加到主compose文件
        cat >> $MAIN_COMPOSE_FILE << EOF
  $CONTAINER_NAME:
    image: openai-regflow
    container_name: $CONTAINER_NAME
    restart: unless-stopped
    volumes:
      - ../.env:/app/.env
      - ./logs$CONTAINER_COUNT:/app/logs
      - ./screenshots$CONTAINER_COUNT:/app/screenshots
      - ./emails$CONTAINER_COUNT:/app/emails
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-dev-shm-usage --js-flags=--max-old-space-size=512
      - EMAIL_PROVIDER_TYPE=custom
      - DOMAIN=$CUSTOM_DOMAIN
      - TEMP_MAIL=$CUSTOM_TEMP_MAIL
      # 代理配置 - 如需要可以在脚本中设置这些环境变量
      # - PROXY_ENABLED=true
      # - PROXY_SERVER=http://proxy-server:port
      # - PROXY_TYPE=http
      # - PROXY_USERNAME=username
      # - PROXY_PASSWORD=password
    cap_add:
      - SYS_ADMIN
    privileged: true
    shm_size: 2gb
    ulimits:
      core:
        soft: 0
        hard: 0
      nofile:
        soft: 65536
        hard: 65536

EOF
        
        # 记录容器信息
        echo "$CONTAINER_NAME: custom-$CUSTOM_DOMAIN-$CONTAINER_COUNT" >> container_info/container_list.txt
        echo -e "${GREEN}已添加容器配置: $CONTAINER_NAME (域名: $CUSTOM_DOMAIN, 临时邮箱: $CUSTOM_TEMP_MAIL)${NC}"
        
        CONTAINER_COUNT=$((CONTAINER_COUNT+1))
    done
    
    # 批量启动所有容器
    if [ $CONTAINER_COUNT -gt 0 ]; then
        echo -e "\n${YELLOW}正在批量启动所有容器...${NC}"
        $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d
        
        # 等待容器启动
        sleep 3
        
        # 显示容器状态
        echo -e "\n${BLUE}当前容器状态:${NC}"
        docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}"
    fi

# 处理批量导入
elif [ "$INPUT_MODE" == "2" ]; then
    # 设置默认文件路径
    DEFAULT_FILE="accounts.txt"

    # 如果默认文件不存在，创建样板文件
    if [ ! -f "$DEFAULT_FILE" ]; then
        echo -e "${YELLOW}未找到默认文件 $DEFAULT_FILE，正在创建样板文件...${NC}"
        cat > $DEFAULT_FILE << 'ACCOUNTS_EOF'
# OpenAI-RegFlow 批量账号导入文件
# 格式: email----password----token----clientId
# 以 # 开头的行将被忽略
# 空行也会被忽略

# 示例账号（请替换为实际信息）：
<EMAIL>----password123----token123----clientId123
<EMAIL>----password456----token456----clientId456
<EMAIL>----password789----token789----clientId789

# 提示：
# 1. 每行一个账号
# 2. 使用四个横线(----)分隔各字段
# 3. 确保没有多余的空格
# 4. token 和 clientId 必须是有效的值
ACCOUNTS_EOF
        echo -e "${GREEN}已创建样板文件: $DEFAULT_FILE${NC}"
        echo -e "${YELLOW}请编辑该文件，添加您的账号信息后重新运行脚本${NC}"
        exit 0
    fi

    echo -e "${YELLOW}可用的导入文件:${NC}"
    echo -e "1) 默认文件: $DEFAULT_FILE"
    echo -e "2) 自定义文件路径"
    read -p "请选择 (1/2): " FILE_CHOICE

    if [ "$FILE_CHOICE" == "1" ]; then
        FILE_PATH="$DEFAULT_FILE"
    else
        read -p "请输入文件路径: " FILE_PATH
    fi

    if [ ! -f "$FILE_PATH" ]; then
        echo -e "${RED}错误: 文件不存在${NC}"
        exit 1
    fi

    # 统计总行数（排除注释和空行）
    TOTAL_ACCOUNTS=$(grep -v '^#' "$FILE_PATH" | grep -v '^[[:space:]]*$' | wc -l)
    echo -e "${BLUE}找到 $TOTAL_ACCOUNTS 个配置${NC}"

    # 读取文件并处理每一行
    PROCESSED_COUNT=0
    while IFS= read -r CONFIG_LINE || [ -n "$CONFIG_LINE" ]; do
        # 跳过空行和注释行
        [[ -z "$CONFIG_LINE" || "$CONFIG_LINE" =~ ^# ]] && continue

        # 去除前后空格
        CONFIG_LINE=$(echo "$CONFIG_LINE" | xargs)

        # 验证输入格式
        if [[ ! $CONFIG_LINE == *----*----* ]]; then
            echo -e "${RED}警告: 跳过格式不正确的行: $CONFIG_LINE${NC}"
            continue
        fi

        PROCESSED_COUNT=$((PROCESSED_COUNT+1))
        echo -e "\n${BLUE}处理第 $PROCESSED_COUNT/$TOTAL_ACCOUNTS 个配置${NC}"

        # 计算分段数量来判断配置类型
        SEGMENT_COUNT=$(echo "$CONFIG_LINE" | grep -o "----" | wc -l)
        
        CONTAINER_NAME="openai-regflow-$CONTAINER_COUNT"

        # 创建容器专用目录
        mkdir -p container_info/logs$CONTAINER_COUNT container_info/screenshots$CONTAINER_COUNT container_info/emails$CONTAINER_COUNT

        # 根据分段数量判断配置类型
        if [ $SEGMENT_COUNT -eq 3 ]; then
            # 标准账号格式: email----password----token----clientId
            EMAIL_PART=$(echo "$CONFIG_LINE" | awk -F'----' '{print $1}')
            echo -e "${GREEN}识别为标准账号配置: $EMAIL_PART${NC}"
            
            # 将容器配置添加到主compose文件
            cat >> $MAIN_COMPOSE_FILE << EOF
  $CONTAINER_NAME:
    image: openai-regflow
    container_name: $CONTAINER_NAME
    restart: unless-stopped
    volumes:
      - ../.env:/app/.env
      - ./logs$CONTAINER_COUNT:/app/logs
      - ./screenshots$CONTAINER_COUNT:/app/screenshots
      - ./emails$CONTAINER_COUNT:/app/emails
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-dev-shm-usage --js-flags=--max-old-space-size=512
      - EMAIL_EASY_CONFIG=$CONFIG_LINE
      # 代理配置 - 如需要可以在脚本中设置这些环境变量
      # - PROXY_ENABLED=true
      # - PROXY_SERVER=http://proxy-server:port
      # - PROXY_TYPE=http
      # - PROXY_USERNAME=username
      # - PROXY_PASSWORD=password
    cap_add:
      - SYS_ADMIN
    privileged: true
    shm_size: 2gb
    ulimits:
      core:
        soft: 0
        hard: 0
      nofile:
        soft: 65536
        hard: 65536

EOF
            # 记录容器信息
            echo "$CONTAINER_NAME: $EMAIL_PART" >> container_info/container_list.txt
            
        elif [ $SEGMENT_COUNT -eq 2 ]; then
            # 自建邮箱格式: 域名----临时邮箱----其他信息
            CUSTOM_DOMAIN=$(echo "$CONFIG_LINE" | awk -F'----' '{print $1}')
            CUSTOM_TEMP_MAIL=$(echo "$CONFIG_LINE" | awk -F'----' '{print $2}')
            echo -e "${GREEN}识别为自建邮箱配置: $CUSTOM_DOMAIN -> $CUSTOM_TEMP_MAIL${NC}"
            
            # 将容器配置添加到主compose文件
            cat >> $MAIN_COMPOSE_FILE << EOF
  $CONTAINER_NAME:
    image: openai-regflow
    container_name: $CONTAINER_NAME
    restart: unless-stopped
    volumes:
      - ../.env:/app/.env
      - ./logs$CONTAINER_COUNT:/app/logs
      - ./screenshots$CONTAINER_COUNT:/app/screenshots
      - ./emails$CONTAINER_COUNT:/app/emails
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-dev-shm-usage --js-flags=--max-old-space-size=512
      - EMAIL_PROVIDER_TYPE=custom
      - DOMAIN=$CUSTOM_DOMAIN
      - TEMP_MAIL=$CUSTOM_TEMP_MAIL
      # 代理配置 - 如需要可以在脚本中设置这些环境变量
      # - PROXY_ENABLED=true
      # - PROXY_SERVER=http://proxy-server:port
      # - PROXY_TYPE=http
      # - PROXY_USERNAME=username
      # - PROXY_PASSWORD=password
    cap_add:
      - SYS_ADMIN
    privileged: true
    shm_size: 2gb
    ulimits:
      core:
        soft: 0
        hard: 0
      nofile:
        soft: 65536
        hard: 65536

EOF
            # 记录容器信息
            echo "$CONTAINER_NAME: custom-$CUSTOM_DOMAIN-$CONTAINER_COUNT" >> container_info/container_list.txt
            
        else
            echo -e "${RED}警告: 无法识别配置格式，跳过: $CONFIG_LINE${NC}"
            continue
        fi

        echo -e "${GREEN}已添加容器配置: $CONTAINER_NAME${NC}"
        CONTAINER_COUNT=$((CONTAINER_COUNT+1))
    done < "$FILE_PATH"

    # 批量启动所有容器
    if [ $CONTAINER_COUNT -gt 0 ]; then
        echo -e "\n${YELLOW}正在批量启动所有容器...${NC}"
        $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d

        # 等待容器启动
        sleep 3

        # 显示容器状态
        echo -e "\n${BLUE}当前容器状态:${NC}"
        docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}"
    fi

else
    # 逐个输入模式
    echo -e "\n${YELLOW}选择账号类型:${NC}"
    echo -e "1) 标准账号 (email----password----token----clientId)"
    echo -e "2) 自建邮箱 (需指定域名和临时邮箱)"
    read -p "请选择 (1/2): " ACCOUNT_TYPE
    
    if [ "$ACCOUNT_TYPE" == "2" ]; then
        # 自建邮箱模式
        echo -e "${YELLOW}自建邮箱输入格式: 域名----临时邮箱----其他信息 (最后一段将被忽略)${NC}"
        echo -e "${YELLOW}示例: <EMAIL>----107.175.63.168-RackNerd${NC}"
        
        while true; do
            echo -e "\n${YELLOW}容器 #$((CONTAINER_COUNT+1)) 配置${NC}"
            echo -e "${YELLOW}请输入自建邮箱配置信息，或输入 'q' 退出${NC}"
            read -p "配置信息: " CUSTOM_CONFIG
            
            # 检查是否退出
            if [[ "$CUSTOM_CONFIG" == "q" || "$CUSTOM_CONFIG" == "Q" ]]; then
                break
            fi
            
            # 验证输入格式
            if [[ ! $CUSTOM_CONFIG == *----* ]]; then
                echo -e "${RED}错误: 输入格式不正确，需要包含 ---- 分隔符${NC}"
                continue
            fi
            
            # 拆分配置信息
            CUSTOM_DOMAIN=$(echo "$CUSTOM_CONFIG" | awk -F'----' '{print $1}')
            CUSTOM_TEMP_MAIL=$(echo "$CUSTOM_CONFIG" | awk -F'----' '{print $2}')
            
            if [ -z "$CUSTOM_DOMAIN" ] || [ -z "$CUSTOM_TEMP_MAIL" ]; then
                echo -e "${RED}错误: 域名和临时邮箱不能为空，跳过此容器${NC}"
                continue
            fi
            
            echo -e "${GREEN}解析结果 - 域名: $CUSTOM_DOMAIN, 临时邮箱: $CUSTOM_TEMP_MAIL${NC}"
            
            CONTAINER_NAME="openai-regflow-$CONTAINER_COUNT"
            
            # 创建容器专用目录
            mkdir -p container_info/logs$CONTAINER_COUNT container_info/screenshots$CONTAINER_COUNT container_info/emails$CONTAINER_COUNT
            
            # 将容器配置添加到主compose文件
            cat >> $MAIN_COMPOSE_FILE << EOF
  $CONTAINER_NAME:
    image: openai-regflow
    container_name: $CONTAINER_NAME
    restart: unless-stopped
    volumes:
      - ../.env:/app/.env
      - ./logs$CONTAINER_COUNT:/app/logs
      - ./screenshots$CONTAINER_COUNT:/app/screenshots
      - ./emails$CONTAINER_COUNT:/app/emails
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-dev-shm-usage --js-flags=--max-old-space-size=512
      - EMAIL_PROVIDER_TYPE=custom
      - DOMAIN=$CUSTOM_DOMAIN
      - TEMP_MAIL=$CUSTOM_TEMP_MAIL
      # 代理配置 - 如需要可以在脚本中设置这些环境变量
      # - PROXY_ENABLED=true
      # - PROXY_SERVER=http://proxy-server:port
      # - PROXY_TYPE=http
      # - PROXY_USERNAME=username
      # - PROXY_PASSWORD=password
    cap_add:
      - SYS_ADMIN
    privileged: true
    shm_size: 2gb
    ulimits:
      core:
        soft: 0
        hard: 0
      nofile:
        soft: 65536
        hard: 65536

EOF
            
            # 记录容器信息
            echo "$CONTAINER_NAME: custom-$CUSTOM_DOMAIN-$CONTAINER_COUNT" >> container_info/container_list.txt
            
            # 启动当前容器
            echo -e "${YELLOW}正在启动容器 $CONTAINER_NAME...${NC}"
            $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d $CONTAINER_NAME
            
            # 检查容器是否成功启动
            sleep 2
            if docker ps | grep -q "$CONTAINER_NAME"; then
                echo -e "${GREEN}容器 $CONTAINER_NAME 启动成功!${NC}"
                
                # 增加计数器
                CONTAINER_COUNT=$((CONTAINER_COUNT+1))
                
                # 显示当前容器列表
                echo -e "${BLUE}当前容器列表:${NC}"
                docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}"
                
                echo -e "${GREEN}容器 #$CONTAINER_COUNT 已添加并启动${NC}"
            else
                echo -e "${RED}错误: 容器 $CONTAINER_NAME 启动失败${NC}"
                # 删除失败的容器配置
                sed -i "/  $CONTAINER_NAME:/,/^$/d" $MAIN_COMPOSE_FILE
                # 删除容器记录
                sed -i "/$CONTAINER_NAME:/d" container_info/container_list.txt
            fi
        done
    else
        # 标准账号模式（原有逻辑）
        while true; do
            echo -e "\n${YELLOW}容器 #$((CONTAINER_COUNT+1)) 配置${NC}"
            echo -e "${YELLOW}请输入账号信息，格式为: email----password----token----clientId${NC}"
            echo -e "${YELLOW}或者输入 'q' 退出${NC}"
            read -p "账号信息: " EMAIL_EASY_CONFIG
            
            # 检查是否退出
            if [[ "$EMAIL_EASY_CONFIG" == "q" || "$EMAIL_EASY_CONFIG" == "Q" ]]; then
                break
            fi
            
            # 验证输入格式
            if [[ ! $EMAIL_EASY_CONFIG == *----*----* ]]; then
                echo -e "${RED}错误: 输入格式不正确，请使用 email----password----token----clientId 格式${NC}"
                continue
            fi
            
            # 提取邮箱作为容器标识
            EMAIL_PART=$(echo "$EMAIL_EASY_CONFIG" | awk -F'----' '{print $1}')
            CONTAINER_NAME="openai-regflow-$CONTAINER_COUNT"
            
            # 创建容器专用目录
            mkdir -p container_info/logs$CONTAINER_COUNT container_info/screenshots$CONTAINER_COUNT container_info/emails$CONTAINER_COUNT
            
            # 将容器配置添加到主compose文件
            cat >> $MAIN_COMPOSE_FILE << EOF
  $CONTAINER_NAME:
    image: openai-regflow
    container_name: $CONTAINER_NAME
    restart: unless-stopped
    volumes:
      - ../.env:/app/.env
      - ./logs$CONTAINER_COUNT:/app/logs
      - ./screenshots$CONTAINER_COUNT:/app/screenshots
      - ./emails$CONTAINER_COUNT:/app/emails
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-dev-shm-usage --js-flags=--max-old-space-size=512
      - EMAIL_EASY_CONFIG=$EMAIL_EASY_CONFIG
      # 代理配置 - 如需要可以在脚本中设置这些环境变量
      # - PROXY_ENABLED=true
      # - PROXY_SERVER=http://proxy-server:port
      # - PROXY_TYPE=http
      # - PROXY_USERNAME=username
      # - PROXY_PASSWORD=password
    cap_add:
      - SYS_ADMIN
    privileged: true
    shm_size: 2gb
    ulimits:
      core:
        soft: 0
        hard: 0
      nofile:
        soft: 65536
        hard: 65536

EOF
            
            # 记录容器信息
            echo "$CONTAINER_NAME: $EMAIL_PART" >> container_info/container_list.txt
            
            # 启动当前容器
            echo -e "${YELLOW}正在启动容器 $CONTAINER_NAME...${NC}"
            $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d $CONTAINER_NAME
            
            # 检查容器是否成功启动
            sleep 2
            if docker ps | grep -q "$CONTAINER_NAME"; then
                echo -e "${GREEN}容器 $CONTAINER_NAME 启动成功!${NC}"
                
                # 增加计数器
                CONTAINER_COUNT=$((CONTAINER_COUNT+1))
                
                # 显示当前容器列表
                echo -e "${BLUE}当前容器列表:${NC}"
                docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}"
                
                echo -e "${GREEN}容器 #$CONTAINER_COUNT 已添加并启动${NC}"
            else
                echo -e "${RED}错误: 容器 $CONTAINER_NAME 启动失败${NC}"
                # 删除失败的容器配置
                sed -i "/  $CONTAINER_NAME:/,/^$/d" $MAIN_COMPOSE_FILE
                # 删除容器记录
                sed -i "/$CONTAINER_NAME:/d" container_info/container_list.txt
            fi
        done
    fi
fi

# 显示总结信息
if [ $CONTAINER_COUNT -gt 0 ]; then
    echo -e "\n${BLUE}=========================================${NC}"
    echo -e "${GREEN}已成功添加 $CONTAINER_COUNT 个容器${NC}"
    echo -e "${YELLOW}容器信息已保存到 container_info 目录${NC}"
    echo -e "${YELLOW}可以使用管理脚本管理所有容器:${NC}"
    echo -e "${BLUE}使用方法:${NC} ./manage.sh"
    
    # 检查manage.sh是否存在，不存在则复制一份
    if [ ! -f "manage.sh" ]; then
        echo -e "${YELLOW}未找到manage.sh，正在创建...${NC}"
        cp ./manage.sh ./container_info/manage.sh
        chmod +x ./container_info/manage.sh
        echo -e "${GREEN}管理脚本已创建: ./container_info/manage.sh${NC}"
    fi
    
    echo -e "${YELLOW}可以使用以下命令直接管理:${NC}"
    echo -e "${BLUE}查看所有容器状态:${NC} ./manage.sh status"
    echo -e "${BLUE}启动所有容器:${NC} ./manage.sh start"
    echo -e "${BLUE}停止所有容器:${NC} ./manage.sh stop"
    echo -e "${BLUE}查看特定容器日志:${NC} ./manage.sh logs <编号>"
    echo -e "${BLUE}添加新容器:${NC} ./manage.sh add"
    echo -e "${BLUE}删除容器:${NC} ./manage.sh remove"
    echo -e "${BLUE}交互式菜单:${NC} ./manage.sh"
    echo -e "${BLUE}=========================================${NC}"

    # 创建示例账号文件
    if [ ! -f "accounts.txt" ]; then
        cat > accounts_example.txt << 'EXAMPLE_EOF'
# OpenAI-RegFlow 批量账号导入文件示例
# 格式: email----password----token----clientId
# 以 # 开头的行将被忽略
# 空行也会被忽略

# 示例账号（请替换为实际信息）：
<EMAIL>----password123----token123----clientId123
<EMAIL>----password456----token456----clientId456
<EMAIL>----password789----token789----clientId789

# 提示：
# 1. 每行一个账号
# 2. 使用四个横线(----)分隔各字段
# 3. 确保没有多余的空格
# 4. token 和 clientId 必须是有效的值
# 5. 将此文件重命名为 accounts.txt 使用
EXAMPLE_EOF
        echo -e "${YELLOW}已创建账号文件示例: accounts_example.txt${NC}"
        echo -e "${YELLOW}编辑后重命名为 accounts.txt 即可使用批量导入功能${NC}"
    fi
    
    # 创建自建邮箱示例配置文件
    if [ ! -f "custom_emails.txt" ]; then
        cat > custom_emails_example.txt << 'CUSTOM_EXAMPLE_EOF'
# OpenAI-RegFlow 自建邮箱批量配置文件示例
# 格式: 域名----临时邮箱----其他信息 (最后一段将被忽略)
# 以 # 开头的行将被忽略
# 空行也会被忽略

# 示例配置（请替换为实际信息）：
<EMAIL>----107.175.63.168-RackNerd
<EMAIL>----192.168.1.100-Server1
<EMAIL>----10.0.0.50-VPS2

# 提示：
# 1. 每行一个配置
# 2. 使用四个横线(----)分隔各字段
# 3. 前两段为域名和临时邮箱，最后一段会被忽略
# 4. 确保域名和邮箱格式正确
# 5. 将此文件重命名为 custom_emails.txt 使用
CUSTOM_EXAMPLE_EOF
        echo -e "${YELLOW}已创建自建邮箱配置示例: custom_emails_example.txt${NC}"
        echo -e "${YELLOW}编辑后重命名为 custom_emails.txt 可通过批量导入功能使用${NC}"
    fi
else
    echo -e "\n${YELLOW}未添加任何容器${NC}"
    echo -e "${YELLOW}提示: 下次运行时可以使用批量导入功能，将账号信息保存到 accounts.txt 文件中${NC}"
fi
