#!/bin/bash

# OpenAI-RegFlow Mac优化版容器管理脚本
# 专为macOS系统设计，支持Apple Silicon和Intel Mac
# 版本: 2.0-mac

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Mac系统信息检测
detect_mac_info() {
    MAC_VERSION=$(sw_vers -productVersion)
    MAC_BUILD=$(sw_vers -buildVersion)
    CPU_ARCH=$(uname -m)
    TOTAL_MEM=$(sysctl -n hw.memsize)
    TOTAL_MEM_GB=$((TOTAL_MEM / 1024 / 1024 / 1024))
    
    if [[ "$CPU_ARCH" == "arm64" ]]; then
        PLATFORM="Apple Silicon (M1/M2/M3)"
    else
        PLATFORM="Intel"
    fi
}

# 检测Docker状态
check_docker_status() {
    if ! docker info >/dev/null 2>&1; then
        echo -e "${RED}❌ Docker未运行${NC}"
        echo -e "${YELLOW}请启动Docker Desktop后重试${NC}"
        return 1
    fi
    
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
    elif command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
    else
        echo -e "${RED}❌ Docker Compose未找到${NC}"
        return 1
    fi
    
    return 0
}

# 显示Mac系统状态
show_mac_status() {
    echo -e "${CYAN}🍎 Mac系统状态${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "系统版本: macOS $MAC_VERSION ($MAC_BUILD)"
    echo -e "处理器架构: $CPU_ARCH ($PLATFORM)"
    echo -e "总内存: ${TOTAL_MEM_GB}GB"
    
    # CPU使用率
    CPU_USAGE=$(top -l 1 -n 0 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
    echo -e "CPU使用率: ${CPU_USAGE}%"
    
    # 内存使用情况
    MEM_INFO=$(memory_pressure 2>/dev/null | grep "System-wide memory free percentage" | awk '{print $5}' | sed 's/%//' || echo "N/A")
    if [[ "$MEM_INFO" != "N/A" ]]; then
        MEM_USED=$((100 - MEM_INFO))
        echo -e "内存使用率: ${MEM_USED}%"
    fi
    
    # Docker Desktop状态
    if docker info >/dev/null 2>&1; then
        DOCKER_VERSION=$(docker --version | awk '{print $3}' | sed 's/,//')
        echo -e "Docker版本: $DOCKER_VERSION"
        
        # Docker资源配置
        DOCKER_CPUS=$(docker system info 2>/dev/null | grep "CPUs:" | awk '{print $2}' || echo "N/A")
        DOCKER_MEM=$(docker system info 2>/dev/null | grep "Total Memory:" | awk '{print $3$4}' || echo "N/A")
        echo -e "Docker配置: CPU ${DOCKER_CPUS}, 内存 ${DOCKER_MEM}"
    else
        echo -e "${RED}Docker: 未运行${NC}"
    fi
    echo
}

# 显示容器状态
show_container_status() {
    echo -e "${BLUE}📦 容器运行状态${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 检查是否有容器运行
    CONTAINERS=$(docker ps --filter "name=openai-regflow" --format "{{.Names}}")
    
    if [ -z "$CONTAINERS" ]; then
        echo -e "${YELLOW}暂无运行中的容器${NC}"
        
        # 检查是否有停止的容器
        STOPPED_CONTAINERS=$(docker ps -a --filter "name=openai-regflow" --format "{{.Names}}")
        if [ ! -z "$STOPPED_CONTAINERS" ]; then
            echo -e "\n${CYAN}已停止的容器:${NC}"
            docker ps -a --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}\t{{.CreatedAt}}"
        fi
    else
        # 显示运行中的容器
        echo -e "${GREEN}运行中的容器:${NC}"
        docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.RunningFor}}"
        
        # 显示资源使用情况
        echo -e "\n${CYAN}资源使用情况:${NC}"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" $(echo "$CONTAINERS" | tr '\n' ' ')
    fi
    echo
}

# 显示详细容器信息
show_detailed_info() {
    echo -e "${PURPLE}🔍 详细容器信息${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    CONTAINERS=$(docker ps -a --filter "name=openai-regflow" --format "{{.Names}}")
    
    if [ -z "$CONTAINERS" ]; then
        echo -e "${YELLOW}未找到任何容器${NC}"
        return
    fi
    
    for container in $CONTAINERS; do
        echo -e "\n${CYAN}容器: $container${NC}"
        
        # 容器状态
        STATUS=$(docker inspect $container --format "{{.State.Status}}")
        echo -e "状态: $STATUS"
        
        # 运行时间
        if [ "$STATUS" == "running" ]; then
            STARTED=$(docker inspect $container --format "{{.State.StartedAt}}")
            echo -e "启动时间: $STARTED"
        fi
        
        # 资源限制
        MEM_LIMIT=$(docker inspect $container --format "{{.HostConfig.Memory}}")
        CPU_LIMIT=$(docker inspect $container --format "{{.HostConfig.CpuQuota}}")
        if [ "$MEM_LIMIT" != "0" ]; then
            MEM_LIMIT_GB=$((MEM_LIMIT / 1024 / 1024 / 1024))
            echo -e "内存限制: ${MEM_LIMIT_GB}GB"
        fi
        
        # 挂载卷
        echo -e "数据目录:"
        docker inspect $container --format "{{range .Mounts}}  {{.Source}} -> {{.Destination}}{{end}}" | head -5
        
        echo -e "${BLUE}────────────────────────────────────────${NC}"
    done
    echo
}

# 容器操作菜单
container_operations_menu() {
    while true; do
        echo -e "${YELLOW}🛠 容器操作菜单${NC}"
        echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo -e "1) 启动所有容器"
        echo -e "2) 停止所有容器"
        echo -e "3) 重启所有容器"
        echo -e "4) 启动特定容器"
        echo -e "5) 停止特定容器"
        echo -e "6) 重启特定容器"
        echo -e "7) 删除停止的容器"
        echo -e "8) 查看容器日志"
        echo -e "9) 进入容器终端"
        echo -e "10) 重新装载.env文件并重启所有容器"
        echo -e "0) 返回主菜单"
        echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        read -p "请选择操作 (0-10): " choice
        
        case $choice in
            1)
                start_all_containers
                ;;
            2)
                stop_all_containers
                ;;
            3)
                restart_all_containers
                ;;
            4)
                start_specific_container
                ;;
            5)
                stop_specific_container
                ;;
            6)
                restart_specific_container
                ;;
            7)
                remove_stopped_containers
                ;;
            8)
                view_container_logs
                ;;
            9)
                enter_container_terminal
                ;;
            10)
                reload_env_and_restart_mac
                ;;
            0)
                break
                ;;
            *)
                echo -e "${RED}无效选择，请重试${NC}"
                ;;
        esac
        echo
    done
}

# 启动所有容器
start_all_containers() {
    echo -e "${YELLOW}启动所有容器...${NC}"
    
    if [ ! -f "container_info/docker-compose.yml" ]; then
        echo -e "${RED}未找到docker-compose.yml文件${NC}"
        echo -e "${YELLOW}请先运行启动脚本创建容器配置${NC}"
        return
    fi
    
    $DOCKER_COMPOSE_CMD -f container_info/docker-compose.yml up -d
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 所有容器已启动${NC}"
        sleep 2
        show_container_status
    else
        echo -e "${RED}❌ 启动失败${NC}"
    fi
}

# 停止所有容器
stop_all_containers() {
    echo -e "${YELLOW}停止所有容器...${NC}"
    
    CONTAINERS=$(docker ps --filter "name=openai-regflow" --format "{{.Names}}")
    if [ -z "$CONTAINERS" ]; then
        echo -e "${YELLOW}没有运行中的容器${NC}"
        return
    fi
    
    docker stop $CONTAINERS
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 所有容器已停止${NC}"
    else
        echo -e "${RED}❌ 停止失败${NC}"
    fi
}

# 重启所有容器
restart_all_containers() {
    echo -e "${YELLOW}重启所有容器...${NC}"
    
    if [ ! -f "container_info/docker-compose.yml" ]; then
        echo -e "${RED}未找到docker-compose.yml文件${NC}"
        return
    fi
    
    $DOCKER_COMPOSE_CMD -f container_info/docker-compose.yml restart
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 所有容器已重启${NC}"
        sleep 3
        show_container_status
    else
        echo -e "${RED}❌ 重启失败${NC}"
    fi
}

# 重新装载.env文件并重启所有容器
reload_env_and_restart_mac() {
    echo -e "${PURPLE}🔄 重新装载.env文件并重启所有容器${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 检查.env文件是否存在
    if [ ! -f ".env" ]; then
        echo -e "${RED}❌ 未找到.env文件${NC}"
        echo -e "${YELLOW}请确保.env文件存在于项目根目录${NC}"
        echo -e "${CYAN}按任意键返回...${NC}"
        read -n 1 -s
        return 1
    fi
    
    echo -e "${YELLOW}📁 检测到.env文件: $(pwd)/.env${NC}"
    echo -e "${CYAN}⚠️  注意: 此操作将停止所有容器，重新读取.env文件，然后重新启动容器${NC}"
    echo
    read -p "确定要继续吗？(y/N): " CONFIRM
    
    if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}操作已取消${NC}"
        echo -e "${CYAN}按任意键返回...${NC}"
        read -n 1 -s
        return 0
    fi
    
    # 显示当前运行的容器
    echo -e "\n${BLUE}📦 当前运行的容器:${NC}"
    docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}\t{{.RunningFor}}"
    
    # 检查docker-compose.yml文件
    if [ ! -f "container_info/docker-compose.yml" ]; then
        echo -e "${RED}❌ 未找到docker-compose.yml文件${NC}"
        echo -e "${YELLOW}请先运行启动脚本创建容器配置${NC}"
        echo -e "${CYAN}按任意键返回...${NC}"
        read -n 1 -s
        return 1
    fi
    
    # 停止所有容器
    echo -e "\n${YELLOW}⏹️  正在停止所有容器...${NC}"
    $DOCKER_COMPOSE_CMD -f container_info/docker-compose.yml down
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 停止容器失败${NC}"
        echo -e "${CYAN}按任意键返回...${NC}"
        read -n 1 -s
        return 1
    fi
    
    echo -e "${GREEN}✅ 所有容器已停止${NC}"
    sleep 2
    
    # 重新启动容器（这会重新读取.env文件）
    echo -e "\n${YELLOW}🚀 正在重新启动所有容器（重新装载.env文件）...${NC}"
    $DOCKER_COMPOSE_CMD -f container_info/docker-compose.yml up -d
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 所有容器已重新启动，.env文件已重新装载${NC}"
        sleep 3
        
        # 显示重启后的状态
        echo -e "\n${BLUE}📊 重启后的容器状态:${NC}"
        show_container_status
        
        echo -e "\n${GREEN}🎉 .env文件重新装载完成！${NC}"
    else
        echo -e "${RED}❌ 重新启动容器失败${NC}"
        echo -e "${YELLOW}请检查docker-compose.yml配置和.env文件内容${NC}"
    fi
    
    echo -e "\n${CYAN}按任意键返回...${NC}"
    read -n 1 -s
}

# 选择特定容器
select_container() {
    local action="$1"
    
    CONTAINERS=$(docker ps -a --filter "name=openai-regflow" --format "{{.Names}}")
    
    if [ -z "$CONTAINERS" ]; then
        echo -e "${YELLOW}未找到任何容器${NC}"
        return 1
    fi
    
    echo -e "${CYAN}可用容器列表:${NC}"
    echo "$CONTAINERS" | nl -w2 -s') '
    echo
    
    read -p "请选择容器编号: " container_num
    
    SELECTED_CONTAINER=$(echo "$CONTAINERS" | sed -n "${container_num}p")
    
    if [ -z "$SELECTED_CONTAINER" ]; then
        echo -e "${RED}无效的容器编号${NC}"
        return 1
    fi
    
    echo "$SELECTED_CONTAINER"
    return 0
}

# 启动特定容器
start_specific_container() {
    CONTAINER=$(select_container "启动")
    [ $? -ne 0 ] && return
    
    echo -e "${YELLOW}启动容器: $CONTAINER${NC}"
    docker start "$CONTAINER"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 容器 $CONTAINER 已启动${NC}"
    else
        echo -e "${RED}❌ 启动失败${NC}"
    fi
}

# 停止特定容器
stop_specific_container() {
    CONTAINER=$(select_container "停止")
    [ $? -ne 0 ] && return
    
    echo -e "${YELLOW}停止容器: $CONTAINER${NC}"
    docker stop "$CONTAINER"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 容器 $CONTAINER 已停止${NC}"
    else
        echo -e "${RED}❌ 停止失败${NC}"
    fi
}

# 重启特定容器
restart_specific_container() {
    CONTAINER=$(select_container "重启")
    [ $? -ne 0 ] && return
    
    echo -e "${YELLOW}重启容器: $CONTAINER${NC}"
    docker restart "$CONTAINER"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 容器 $CONTAINER 已重启${NC}"
    else
        echo -e "${RED}❌ 重启失败${NC}"
    fi
}

# 删除停止的容器
remove_stopped_containers() {
    STOPPED_CONTAINERS=$(docker ps -a --filter "name=openai-regflow" --filter "status=exited" --format "{{.Names}}")
    
    if [ -z "$STOPPED_CONTAINERS" ]; then
        echo -e "${YELLOW}没有停止的容器需要删除${NC}"
        return
    fi
    
    echo -e "${CYAN}停止的容器:${NC}"
    echo "$STOPPED_CONTAINERS"
    echo
    
    read -p "确认删除这些容器? (y/N): " confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        docker rm $STOPPED_CONTAINERS
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 已删除停止的容器${NC}"
        else
            echo -e "${RED}❌ 删除失败${NC}"
        fi
    else
        echo -e "${YELLOW}操作已取消${NC}"
    fi
}

# 查看容器日志
view_container_logs() {
    CONTAINER=$(select_container "查看日志")
    [ $? -ne 0 ] && return
    
    echo -e "${CYAN}选择日志查看方式:${NC}"
    echo -e "1) 查看最新50行日志"
    echo -e "2) 实时跟踪日志"
    echo -e "3) 查看全部日志"
    
    read -p "请选择 (1-3): " log_choice
    
    case $log_choice in
        1)
            docker logs --tail 50 "$CONTAINER"
            ;;
        2)
            echo -e "${YELLOW}按Ctrl+C退出日志跟踪${NC}"
            docker logs -f "$CONTAINER"
            ;;
        3)
            docker logs "$CONTAINER" | less
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 进入容器终端
enter_container_terminal() {
    RUNNING_CONTAINERS=$(docker ps --filter "name=openai-regflow" --format "{{.Names}}")
    
    if [ -z "$RUNNING_CONTAINERS" ]; then
        echo -e "${YELLOW}没有运行中的容器${NC}"
        return
    fi
    
    echo -e "${CYAN}运行中的容器:${NC}"
    echo "$RUNNING_CONTAINERS" | nl -w2 -s') '
    echo
    
    read -p "请选择容器编号: " container_num
    
    SELECTED_CONTAINER=$(echo "$RUNNING_CONTAINERS" | sed -n "${container_num}p")
    
    if [ -z "$SELECTED_CONTAINER" ]; then
        echo -e "${RED}无效的容器编号${NC}"
        return
    fi
    
    echo -e "${YELLOW}进入容器终端: $SELECTED_CONTAINER${NC}"
    echo -e "${CYAN}输入 'exit' 退出容器终端${NC}"
    docker exec -it "$SELECTED_CONTAINER" /bin/bash
}

# Mac性能监控
mac_performance_monitor() {
    echo -e "${PURPLE}📊 Mac性能监控${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    echo -e "${CYAN}系统资源使用情况:${NC}"
    
    # CPU使用率
    echo -e "\n${YELLOW}🔥 CPU使用率:${NC}"
    top -l 1 -n 10 -o cpu | grep -E "^[0-9]" | head -5
    
    # 内存使用情况
    echo -e "\n${YELLOW}💾 内存使用情况:${NC}"
    vm_stat | grep -E "(free|active|inactive|wired|compressed)"
    
    # 磁盘使用情况
    echo -e "\n${YELLOW}💿 磁盘使用情况:${NC}"
    df -h / | tail -1
    
    # Docker资源使用
    if docker info >/dev/null 2>&1; then
        echo -e "\n${YELLOW}🐳 Docker容器资源使用:${NC}"
        CONTAINERS=$(docker ps --filter "name=openai-regflow" --format "{{.Names}}")
        if [ ! -z "$CONTAINERS" ]; then
            docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}" $CONTAINERS
        else
            echo -e "${YELLOW}没有运行中的容器${NC}"
        fi
    fi
    
    echo -e "\n${CYAN}按任意键返回...${NC}"
    read -n 1 -s
}

# 实时性能监控
realtime_monitor() {
    echo -e "${PURPLE}📈 实时性能监控 (按q退出)${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    while true; do
        clear
        echo -e "${PURPLE}📈 实时性能监控 (按q退出)${NC}"
        echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        # 系统概况
        echo -e "${CYAN}系统时间: $(date)${NC}"
        echo -e "${CYAN}运行时间: $(uptime | awk '{print $3,$4}' | sed 's/,//')${NC}"
        
        # CPU和内存
        CPU_USAGE=$(top -l 1 -n 0 | grep "CPU usage" | awk '{print $3}')
        echo -e "${YELLOW}CPU使用率: $CPU_USAGE${NC}"
        
        # 容器状态
        echo -e "\n${BLUE}容器状态:${NC}"
        CONTAINERS=$(docker ps --filter "name=openai-regflow" --format "{{.Names}}")
        if [ ! -z "$CONTAINERS" ]; then
            docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" $CONTAINERS
        else
            echo -e "${YELLOW}没有运行中的容器${NC}"
        fi
        
        echo -e "\n${CYAN}按q退出，任意键刷新...${NC}"
        
        # 等待用户输入
        read -t 3 -n 1 key
        if [[ $key == "q" ]]; then
            break
        fi
    done
}

# Docker管理菜单
docker_management_menu() {
    while true; do
        echo -e "${BLUE}🐳 Docker管理菜单${NC}"
        echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo -e "1) 查看Docker系统信息"
        echo -e "2) 清理Docker资源"
        echo -e "3) 重建Mac优化镜像"
        echo -e "4) 查看镜像列表"
        echo -e "5) 查看网络列表"
        echo -e "6) 查看卷列表"
        echo -e "7) Docker Desktop设置检查"
        echo -e "0) 返回主菜单"
        echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        read -p "请选择操作 (0-7): " choice
        
        case $choice in
            1)
                show_docker_info
                ;;
            2)
                cleanup_docker_resources
                ;;
            3)
                rebuild_mac_image
                ;;
            4)
                show_docker_images
                ;;
            5)
                show_docker_networks
                ;;
            6)
                show_docker_volumes
                ;;
            7)
                check_docker_settings
                ;;
            0)
                break
                ;;
            *)
                echo -e "${RED}无效选择，请重试${NC}"
                ;;
        esac
        echo
    done
}

# 显示Docker系统信息
show_docker_info() {
    echo -e "${CYAN}🐳 Docker系统信息${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    docker system info
    echo
    echo -e "${CYAN}按任意键返回...${NC}"
    read -n 1 -s
}

# 清理Docker资源
cleanup_docker_resources() {
    echo -e "${YELLOW}🧹 Docker资源清理${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    echo -e "${CYAN}选择清理选项:${NC}"
    echo -e "1) 清理停止的容器"
    echo -e "2) 清理未使用的镜像"
    echo -e "3) 清理未使用的网络"
    echo -e "4) 清理未使用的卷"
    echo -e "5) 全面清理 (谨慎)"
    echo -e "0) 取消"
    
    read -p "请选择 (0-5): " cleanup_choice
    
    case $cleanup_choice in
        1)
            docker container prune -f
            echo -e "${GREEN}✅ 已清理停止的容器${NC}"
            ;;
        2)
            docker image prune -f
            echo -e "${GREEN}✅ 已清理未使用的镜像${NC}"
            ;;
        3)
            docker network prune -f
            echo -e "${GREEN}✅ 已清理未使用的网络${NC}"
            ;;
        4)
            docker volume prune -f
            echo -e "${GREEN}✅ 已清理未使用的卷${NC}"
            ;;
        5)
            read -p "确认进行全面清理? 这将删除所有未使用的资源 (y/N): " confirm
            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                docker system prune -a -f
                echo -e "${GREEN}✅ 已完成全面清理${NC}"
            else
                echo -e "${YELLOW}操作已取消${NC}"
            fi
            ;;
        0)
            echo -e "${YELLOW}操作已取消${NC}"
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 重建Mac优化镜像
rebuild_mac_image() {
    echo -e "${YELLOW}🔄 重建Mac优化镜像${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    echo -e "${CYAN}构建选项:${NC}"
    echo -e "1) 标准重建"
    echo -e "2) 强制重建 (清除缓存)"
    echo -e "0) 取消"
    
    read -p "请选择 (0-2): " build_choice
    
    case $build_choice in
        1)
            echo -e "${YELLOW}开始标准重建...${NC}"
            ./start_many_mac.sh update
            ;;
        2)
            echo -e "${YELLOW}开始强制重建 (清除缓存)...${NC}"
            docker rmi openai-regflow:mac -f 2>/dev/null || true
            docker builder prune -f
            ./start_many_mac.sh update
            ;;
        0)
            echo -e "${YELLOW}操作已取消${NC}"
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 显示Docker镜像
show_docker_images() {
    echo -e "${CYAN}🖼 Docker镜像列表${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    echo
    echo -e "${CYAN}按任意键返回...${NC}"
    read -n 1 -s
}

# 显示Docker网络
show_docker_networks() {
    echo -e "${CYAN}🌐 Docker网络列表${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    docker network ls
    echo
    echo -e "${CYAN}按任意键返回...${NC}"
    read -n 1 -s
}

# 显示Docker卷
show_docker_volumes() {
    echo -e "${CYAN}💾 Docker卷列表${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    docker volume ls
    echo
    echo -e "${CYAN}按任意键返回...${NC}"
    read -n 1 -s
}

# 检查Docker Desktop设置
check_docker_settings() {
    echo -e "${CYAN}⚙️ Docker Desktop设置检查${NC}"
    echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 检查Docker Desktop资源配置
    echo -e "${YELLOW}Docker Desktop资源配置:${NC}"
    DOCKER_CPUS=$(docker system info 2>/dev/null | grep "CPUs:" | awk '{print $2}' || echo "未知")
    DOCKER_MEM=$(docker system info 2>/dev/null | grep "Total Memory:" | awk '{print $3$4}' || echo "未知")
    
    echo -e "分配CPU: $DOCKER_CPUS"
    echo -e "分配内存: $DOCKER_MEM"
    
    # 建议配置
    echo -e "\n${YELLOW}Mac推荐配置:${NC}"
    if [ $TOTAL_MEM_GB -ge 16 ]; then
        echo -e "CPU: 6-8核"
        echo -e "内存: 8-12GB"
    elif [ $TOTAL_MEM_GB -ge 8 ]; then
        echo -e "CPU: 4-6核"
        echo -e "内存: 4-6GB"
    else
        echo -e "CPU: 2-4核"
        echo -e "内存: 2-4GB"
    fi
    
    echo -e "\n${CYAN}优化建议:${NC}"
    echo -e "1. 确保Docker Desktop使用Virtualization framework"
    if [[ "$CPU_ARCH" == "arm64" ]]; then
        echo -e "2. Apple Silicon Mac建议启用Rosetta模拟"
    fi
    echo -e "3. 分配至少50%的系统内存给Docker"
    echo -e "4. 定期更新Docker Desktop到最新版本"
    
    echo -e "\n${CYAN}按任意键返回...${NC}"
    read -n 1 -s
}

# 主菜单
main_menu() {
    while true; do
        clear
        echo -e "${PURPLE}╔══════════════════════════════════════════╗${NC}"
        echo -e "${PURPLE}║        OpenAI-RegFlow Mac管理器          ║${NC}"
        echo -e "${PURPLE}╚══════════════════════════════════════════╝${NC}"
        
        # 显示系统信息
        show_mac_status
        show_container_status
        
        echo -e "${YELLOW}📋 主菜单${NC}"
        echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo -e "1) 🛠  容器操作管理"
        echo -e "2) 🔍 详细容器信息"
        echo -e "3) 📊 Mac性能监控"
        echo -e "4) 📈 实时性能监控"
        echo -e "5) 🐳 Docker管理"
        echo -e "6) 🔄 刷新状态"
        echo -e "0) 🚪 退出"
        echo -e "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        read -p "请选择操作 (0-6): " choice
        
        case $choice in
            1)
                container_operations_menu
                ;;
            2)
                show_detailed_info
                echo -e "${CYAN}按任意键返回...${NC}"
                read -n 1 -s
                ;;
            3)
                mac_performance_monitor
                ;;
            4)
                realtime_monitor
                ;;
            5)
                docker_management_menu
                ;;
            6)
                echo -e "${YELLOW}刷新状态...${NC}"
                sleep 1
                ;;
            0)
                echo -e "${GREEN}感谢使用OpenAI-RegFlow Mac管理器！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重试${NC}"
                sleep 1
                ;;
        esac
    done
}

# 脚本入口
main() {
    # 检测Mac系统信息
    detect_mac_info
    
    # 检查Docker状态
    if ! check_docker_status; then
        exit 1
    fi
    
    # 处理命令行参数
    case "${1:-}" in
        "status")
            show_mac_status
            show_container_status
            ;;
        "start")
            start_all_containers
            ;;
        "stop")
            stop_all_containers
            ;;
        "restart")
            restart_all_containers
            ;;
        "reload-env")
            reload_env_and_restart_mac
            ;;
        "logs")
            if [ -n "$2" ]; then
                docker logs -f "openai-regflow-$2"
            else
                view_container_logs
            fi
            ;;
        "monitor")
            realtime_monitor
            ;;
        *)
            main_menu
            ;;
    esac
}

# 运行主程序
main "$@" 