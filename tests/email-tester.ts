import { MicrosoftEmailProvider } from '../src/providers/email/microsoft';
import { logger } from '../src/utils/logger';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, '../.env') });

/**
 * 测试Microsoft邮箱提供者是否能正确获取邮件
 */
class MicrosoftEmailTester {
    private clientId: string;
    private refreshToken: string;
    private email: string;

    constructor() {
        // 从环境变量获取凭证
        this.clientId = process.env.CLIENT_ID || '';
        this.refreshToken = process.env.REFRESH_TOKEN || '';
        this.email = process.env.EMAIL_ADDRESS || '';

        if (!this.clientId || !this.refreshToken || !this.email) {
            throw new Error('缺少必要的环境变量: CLIENT_ID, REFRESH_TOKEN, EMAIL_ADDRESS');
        }
    }

    /**
     * 测试邮箱认证
     */
    async testAuthentication(): Promise<boolean> {
        console.log('=== 测试邮箱认证 ===');

        const provider = new MicrosoftEmailProvider({
            clientId: this.clientId,
            refreshToken: this.refreshToken,
            email: this.email
        });

        try {
            const result = await provider.authenticate();
            if (result) {
                console.log('✅ 认证成功!');
            } else {
                console.log('❌ 认证失败!');
            }
            return result;
        } catch (error) {
            console.error('❌ 认证过程中出错:', error);
            return false;
        }
    }

    /**
     * 测试获取邮件
     */
    async testFetchEmails(): Promise<void> {
        console.log('\n=== 测试获取邮件 ===');

        const provider = new MicrosoftEmailProvider({
            clientId: this.clientId,
            refreshToken: this.refreshToken,
            email: this.email
        });

        try {
            // 先认证
            const authenticated = await provider.authenticate();
            if (!authenticated) {
                console.log('❌ 无法测试获取邮件，因为认证失败');
                return;
            }

            // 通过反射直接访问私有方法进行测试
            const fetchEmails = (provider as any).connectToPOP3.bind(provider);

            console.log('正在获取邮件...');
            const emails = await fetchEmails();

            console.log(`✅ 成功获取到 ${emails.length} 封邮件`);

            // 保存第一封邮件用于分析
            if (emails.length > 0) {
                const sampleEmail = emails[0];
                const samplePath = path.join(__dirname, 'sample-email.txt');

                fs.writeFileSync(samplePath, sampleEmail);
                console.log(`已保存示例邮件到: ${samplePath}`);

                // 显示邮件摘要
                const preview = sampleEmail.length > 200
                    ? sampleEmail.substring(0, 200) + '...'
                    : sampleEmail;

                console.log('\n邮件预览:');
                console.log(preview);
            }
        } catch (error) {
            console.error('❌ 获取邮件过程中出错:', error);
        }
    }

    /**
     * 测试提取验证码
     */
    async testVerificationCode(): Promise<void> {
        console.log('\n=== 测试获取验证码 ===');

        const provider = new MicrosoftEmailProvider({
            clientId: this.clientId,
            refreshToken: this.refreshToken,
            email: this.email
        });

        try {
            // 设置时间范围，获取最近10分钟的邮件
            const fromTime = new Date(Date.now() - 10 * 60 * 1000);

            console.log(`正在尝试获取自 ${fromTime.toLocaleTimeString()} 以来的验证码...`);

            const result = await provider.getVerificationCode(fromTime);

            if (result.success && result.code) {
                console.log(`✅ 成功获取到验证码: ${result.code}`);
            } else {
                console.log(`❌ 未找到验证码: ${result.error || '未知错误'}`);
            }
        } catch (error) {
            console.error('❌ 获取验证码过程中出错:', error);
        }
    }

    /**
     * 测试从邮件中直接提取验证码
     */
    async testExtractCode(): Promise<void> {
        console.log('\n=== 测试直接提取验证码 ===');

        const provider = new MicrosoftEmailProvider({
            clientId: this.clientId,
            refreshToken: this.refreshToken,
            email: this.email
        });

        // 检查是否存在示例邮件
        const samplePath = path.join(__dirname, 'sample-email.txt');
        if (!fs.existsSync(samplePath)) {
            console.log('❌ 未找到示例邮件文件。请先运行获取邮件的测试。');
            return;
        }

        try {
            const emailContent = fs.readFileSync(samplePath, 'utf8');

            // 通过反射访问私有方法
            const extractCode = (provider as any).extractVerificationCode.bind(provider);

            console.log('从示例邮件中提取验证码...');
            const code = await extractCode(emailContent);

            if (code) {
                console.log(`✅ 成功提取验证码: ${code}`);
            } else {
                console.log('❌ 无法从示例邮件中提取验证码');
            }
        } catch (error) {
            console.error('❌ 提取验证码过程中出错:', error);
        }
    }

    /**
     * 运行所有测试
     */
    async runAllTests(): Promise<void> {
        console.log('开始测试Microsoft邮箱功能...');
        console.log(`邮箱: ${this.email}`);

        const authResult = await this.testAuthentication();

        if (authResult) {
            await this.testFetchEmails();
            await this.testVerificationCode();
            await this.testExtractCode();
        }

        console.log('\n测试完成!');
    }
}

// 运行测试
const tester = new MicrosoftEmailTester();
tester.runAllTests().catch(error => {
    console.error('测试过程中出现未处理的错误:', error);
});
