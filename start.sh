#!/bin/bash

# 确保脚本在错误时退出
set -e

# 创建必要的目录
mkdir -p logs data

# 检查.env文件是否存在
if [ ! -f .env ]; then
  echo "错误: .env 文件不存在!"
  echo "请先创建 .env 文件并配置必要的环境变量"
  exit 1
fi

# 显示程序版本信息
VERSION="1.0.2-崩溃自动重启版"
echo "OpenAI-RegFlow Docker 管理脚本 v${VERSION}"
echo "=============================="
echo "此版本修复了浏览器崩溃问题，遇到致命错误自动退出重启"
echo "=============================="
echo "1. 构建并启动服务"
echo "2. 启动服务"
echo "3. 停止服务"
echo "4. 查看容器日志"
echo "5. 查看详细日志文件 (combined.log)"
echo "6. 重启服务"
echo "7. 删除容器"
echo "8. 更新到最新版本"
echo "0. 退出"

read -p "请选择操作 [0-8]: " choice

case $choice in
  1)
    echo "正在构建并启动服务..."
    docker compose up -d --build
    echo "服务已启动!"
    ;;
  2)
    echo "正在启动服务..."
    docker compose up -d
    echo "服务已启动!"
    ;;
  3)
    echo "正在停止服务..."
    docker compose down
    echo "服务已停止!"
    ;;
  4)
    echo "显示容器日志 (按 Ctrl+C 退出)..."
    docker compose logs -f
    ;;
  5)
    echo "显示详细日志文件 (按 Ctrl+C 退出)..."
    tail -f logs/combined.log
    ;;
  6)
    echo "正在重启服务..."
    docker compose restart
    echo "服务已重启!"
    ;;
  7)
    echo "正在删除容器..."
    docker compose down
    echo "容器已删除!"
    ;;
  8)
    echo "正在更新到最新版本..."
    git pull
    echo "代码已更新，现在需要重新构建..."
    docker compose down
    docker compose up -d --build
    echo "更新完成并重新启动!"
    ;;
  0)
    echo "退出"
    exit 0
    ;;
  *)
    echo "无效选择!"
    exit 1
    ;;
esac 