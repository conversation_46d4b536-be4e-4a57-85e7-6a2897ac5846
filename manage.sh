#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 自动检测并设置正确的 docker-compose 命令
detect_docker_compose() {
    # 优先检查 docker compose (v2)
    if docker compose version &> /dev/null; then
        echo "docker compose"
        return 0
    fi

    # 检查 docker-compose (v1)
    if command -v docker-compose &> /dev/null; then
        echo "docker-compose"
        return 0
    fi

    # 都没找到
    return 1
}

# 自动检测 Docker Compose
DOCKER_COMPOSE_CMD=$(detect_docker_compose)

if [ -z "$DOCKER_COMPOSE_CMD" ]; then
    echo -e "${RED}错误: 未找到 Docker Compose，请先安装 Docker Compose${NC}"
    exit 1
fi

# 配置文件路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
if [ -d "$SCRIPT_DIR/container_info" ]; then
    # 直接在项目根目录运行
    CONTAINER_INFO_DIR="$SCRIPT_DIR/container_info"
    COMPOSE_FILE="$CONTAINER_INFO_DIR/docker-compose.yml"
    PROJECT_ROOT="$SCRIPT_DIR"
elif [ -f "$SCRIPT_DIR/docker-compose.yml" ]; then
    # 在项目根目录运行，使用根目录的docker-compose.yml
    CONTAINER_INFO_DIR="$SCRIPT_DIR"
    COMPOSE_FILE="$SCRIPT_DIR/docker-compose.yml"
    PROJECT_ROOT="$SCRIPT_DIR"
else
    echo -e "${RED}错误: 无法找到docker-compose.yml文件${NC}"
    echo -e "${YELLOW}请在项目根目录或container_info目录运行此脚本${NC}"
    exit 1
fi

# 确保容器列表文件存在
CONTAINER_LIST_FILE="$CONTAINER_INFO_DIR/container_list.txt"
touch $CONTAINER_LIST_FILE 2>/dev/null || {
    echo -e "${RED}错误: 无法创建或访问容器列表文件${NC}"
    exit 1
}

# 显示容器列表
show_containers() {
    echo -e "${BLUE}当前容器列表:${NC}"
    if [ -s "$CONTAINER_LIST_FILE" ]; then
        cat -n "$CONTAINER_LIST_FILE"
    else
        echo -e "${YELLOW}暂无容器${NC}"
    fi
    
    echo -e "\n${BLUE}容器运行状态:${NC}"
    docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# 添加新容器
add_container() {
    # 获取当前容器数量 - 修复数量计算方法
    CONTAINER_COUNT=0
    if [ -s "$CONTAINER_LIST_FILE" ]; then
        CONTAINER_COUNT=$(grep -c "openai-regflow-" "$CONTAINER_LIST_FILE" || echo 0)
    fi
    
    echo -e "${YELLOW}正在添加新容器 #$((CONTAINER_COUNT+1))${NC}"
    
    # 选择账号类型
    echo -e "${YELLOW}选择账号类型:${NC}"
    echo -e "1) 标准账号 (email----password----token----clientId)"
    echo -e "2) 自建邮箱 (需指定域名和临时邮箱)"
    read -p "请选择 (1/2): " ACCOUNT_TYPE
    
    # 处理自建邮箱模式
    if [ "$ACCOUNT_TYPE" == "2" ]; then
        # 询问域名和临时邮箱
        read -p "请输入域名 (例如: gpt7.link): " CUSTOM_DOMAIN
        read -p "请输入临时邮箱 (例如: <EMAIL>): " CUSTOM_TEMP_MAIL
        
        if [ -z "$CUSTOM_DOMAIN" ] || [ -z "$CUSTOM_TEMP_MAIL" ]; then
            echo -e "${RED}错误: 域名和临时邮箱不能为空${NC}"
            return 1
        fi
        
        CONTAINER_NAME="openai-regflow-$CONTAINER_COUNT"
        
        # 创建容器专用目录
        mkdir -p "$CONTAINER_INFO_DIR/logs$CONTAINER_COUNT" "$CONTAINER_INFO_DIR/screenshots$CONTAINER_COUNT" "$CONTAINER_INFO_DIR/emails$CONTAINER_COUNT"
        
        # 添加到docker-compose.yml
        # 检查文件是否存在且末尾是否有services部分
        if [ ! -f "$COMPOSE_FILE" ]; then
            # 创建新的compose文件
            cat > "$COMPOSE_FILE" << EOF
version: '3.8'

services:
EOF
        fi
        
        # 添加容器配置
        cat >> "$COMPOSE_FILE" << EOF
  $CONTAINER_NAME:
    image: openai-regflow
    container_name: $CONTAINER_NAME
    restart: unless-stopped
    volumes:
      - ../.env:/app/.env
      - ./logs$CONTAINER_COUNT:/app/logs
      - ./screenshots$CONTAINER_COUNT:/app/screenshots
      - ./emails$CONTAINER_COUNT:/app/emails
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-dev-shm-usage --js-flags=--max-old-space-size=512
      - EMAIL_PROVIDER_TYPE=custom
      - DOMAIN=$CUSTOM_DOMAIN
      - TEMP_MAIL=$CUSTOM_TEMP_MAIL
    cap_add:
      - SYS_ADMIN
    privileged: true
    shm_size: 2gb
    ulimits:
      core:
        soft: 0
        hard: 0
      nofile:
        soft: 65536
        hard: 65536

EOF
        
        # 记录容器信息
        echo "$CONTAINER_NAME: custom-$CUSTOM_DOMAIN-$CONTAINER_COUNT" >> "$CONTAINER_LIST_FILE"
    else
        # 标准账号模式（原有逻辑）
        echo -e "${YELLOW}请输入账号信息，格式为: email----password----token----clientId${NC}"
        read -p "账号信息: " EMAIL_EASY_CONFIG
        
        # 验证输入格式
        if [[ ! $EMAIL_EASY_CONFIG == *----*----* ]]; then
            echo -e "${RED}错误: 输入格式不正确，请使用 email----password----token----clientId 格式${NC}"
            return 1
        fi
        
        # 提取邮箱作为容器标识
        EMAIL_PART=$(echo $EMAIL_EASY_CONFIG | cut -d'-' -f1)
        CONTAINER_NAME="openai-regflow-$CONTAINER_COUNT"
        
        # 创建容器专用目录
        mkdir -p "$CONTAINER_INFO_DIR/logs$CONTAINER_COUNT" "$CONTAINER_INFO_DIR/screenshots$CONTAINER_COUNT" "$CONTAINER_INFO_DIR/emails$CONTAINER_COUNT"
        
        # 添加到docker-compose.yml
        # 检查文件是否存在且末尾是否有services部分
        if [ ! -f "$COMPOSE_FILE" ]; then
            # 创建新的compose文件
            cat > "$COMPOSE_FILE" << EOF
version: '3.8'

services:
EOF
        fi
        
        # 添加容器配置
        cat >> "$COMPOSE_FILE" << EOF
  $CONTAINER_NAME:
    image: openai-regflow
    container_name: $CONTAINER_NAME
    restart: unless-stopped
    volumes:
      - ../.env:/app/.env
      - ./logs$CONTAINER_COUNT:/app/logs
      - ./screenshots$CONTAINER_COUNT:/app/screenshots
      - ./emails$CONTAINER_COUNT:/app/emails
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-dev-shm-usage --js-flags=--max-old-space-size=512
      - EMAIL_EASY_CONFIG=$EMAIL_EASY_CONFIG
    cap_add:
      - SYS_ADMIN
    privileged: true
    shm_size: 2gb
    ulimits:
      core:
        soft: 0
        hard: 0
      nofile:
        soft: 65536
        hard: 65536

EOF
        
        # 记录容器信息
        echo "$CONTAINER_NAME: $EMAIL_PART" >> "$CONTAINER_LIST_FILE"
    fi
    
    # 启动新容器
    echo -e "${YELLOW}正在启动容器 $CONTAINER_NAME...${NC}"
    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" up -d $CONTAINER_NAME
    
    # 检查容器是否成功启动
    sleep 2
    if docker ps | grep -q "$CONTAINER_NAME"; then
        echo -e "${GREEN}容器 $CONTAINER_NAME 添加并启动成功!${NC}"
    else
        echo -e "${RED}错误: 容器 $CONTAINER_NAME 启动失败${NC}"
        # 删除失败的容器配置
        sed -i "/  $CONTAINER_NAME:/,/^$/d" "$COMPOSE_FILE"
        # 删除容器记录
        sed -i "/$CONTAINER_NAME:/d" "$CONTAINER_LIST_FILE"
        return 1
    fi
    
    return 0
}

# 修改容器账号
edit_account() {
    # 显示容器列表
    show_containers
    
    # 选择要修改的容器
    echo -e "${YELLOW}请输入要修改账号的容器编号:${NC}"
    read -p "容器编号: " CONTAINER_NUM
    
    # 验证输入
    if ! [[ "$CONTAINER_NUM" =~ ^[0-9]+$ ]]; then
        echo -e "${RED}错误: 请输入有效的容器编号${NC}"
        return 1
    fi
    
    # 获取容器名称
    CONTAINER_NAME=$(sed -n "${CONTAINER_NUM}p" "$CONTAINER_LIST_FILE" | cut -d':' -f1 | xargs)
    CONTAINER_INFO=$(sed -n "${CONTAINER_NUM}p" "$CONTAINER_LIST_FILE" | cut -d':' -f2- | xargs)
    
    if [ -z "$CONTAINER_NAME" ]; then
        echo -e "${RED}错误: 容器编号 $CONTAINER_NUM 不存在${NC}"
        return 1
    fi
    
    # 检查是否是自建邮箱容器
    if [[ "$CONTAINER_INFO" == custom-* ]]; then
        echo -e "${YELLOW}检测到自建邮箱容器，请选择修改方式:${NC}"
        echo -e "1) 更改为标准账号"
        echo -e "2) 更改自建邮箱参数"
        read -p "请选择 (1/2): " EDIT_MODE
        
        if [ "$EDIT_MODE" == "1" ]; then
            # 更改为标准账号
            echo -e "${YELLOW}请输入新的账号信息，格式为: email----password----token----clientId${NC}"
            read -p "新账号信息: " NEW_EMAIL_EASY_CONFIG
            
            # 验证输入格式
            if [[ ! $NEW_EMAIL_EASY_CONFIG == *----*----* ]]; then
                echo -e "${RED}错误: 输入格式不正确，请使用 email----password----token----clientId 格式${NC}"
                return 1
            fi
            
            # 提取邮箱作为容器标识
            NEW_EMAIL_PART=$(echo $NEW_EMAIL_EASY_CONFIG | cut -d'-' -f1)
            
            # 临时文件保存修改
            TEMP_FILE=$(mktemp)
            
            # 修改compose文件，将自建邮箱配置替换为标准账号配置
            awk -v container="$CONTAINER_NAME:" -v new_config="$NEW_EMAIL_EASY_CONFIG" '
            BEGIN { in_container = 0; found_email_type = 0; found_domain = 0; found_temp_mail = 0; }
            {
                if ($0 ~ "  " container) { 
                    in_container = 1; 
                    print $0;
                }
                else if (in_container && $0 ~ /^  [a-zA-Z]/) { 
                    in_container = 0; 
                    print $0;
                }
                else if (in_container && $0 ~ /EMAIL_PROVIDER_TYPE=custom/) {
                    print "      - EMAIL_EASY_CONFIG=" new_config;
                    found_email_type = 1;
                }
                else if (in_container && ($0 ~ /DOMAIN=/ || $0 ~ /TEMP_MAIL=/)) {
                    # 跳过DOMAIN和TEMP_MAIL行
                    if ($0 ~ /DOMAIN=/) found_domain = 1;
                    if ($0 ~ /TEMP_MAIL=/) found_temp_mail = 1;
                }
                else {
                    print $0;
                }
                
                # 如果是容器的最后一行环境变量，并且没有添加EMAIL_EASY_CONFIG，则添加
                if (in_container && !found_email_type && $0 ~ /PUPPETEER_ARGS=/) {
                    print "      - EMAIL_EASY_CONFIG=" new_config;
                }
            }' "$COMPOSE_FILE" > "$TEMP_FILE"
            
            # 检查是否成功修改
            if [ $? -ne 0 ]; then
                echo -e "${RED}错误: 无法修改compose文件${NC}"
                rm "$TEMP_FILE"
                return 1
            fi
            
            # 应用更改
            cp "$TEMP_FILE" "$COMPOSE_FILE"
            rm "$TEMP_FILE"
            
            # 更新容器列表
            sed -i "${CONTAINER_NUM}s/: .*/: $NEW_EMAIL_PART/" "$CONTAINER_LIST_FILE"
        else
            # 更改自建邮箱参数
            read -p "请输入新的域名 (例如: gpt7.link): " NEW_CUSTOM_DOMAIN
            read -p "请输入新的临时邮箱 (例如: <EMAIL>): " NEW_CUSTOM_TEMP_MAIL
            
            if [ -z "$NEW_CUSTOM_DOMAIN" ] || [ -z "$NEW_CUSTOM_TEMP_MAIL" ]; then
                echo -e "${RED}错误: 域名和临时邮箱不能为空${NC}"
                return 1
            fi
            
            # 临时文件保存修改
            TEMP_FILE=$(mktemp)
            
            # 修改compose文件中的环境变量
            awk -v container="$CONTAINER_NAME:" -v new_domain="$NEW_CUSTOM_DOMAIN" -v new_temp_mail="$NEW_CUSTOM_TEMP_MAIL" '
            BEGIN { in_container = 0; }
            {
                if ($0 ~ "  " container) { in_container = 1; }
                else if (in_container && $0 ~ /^  [a-zA-Z]/) { in_container = 0; }
                
                if (in_container && $0 ~ /DOMAIN=/) {
                    print "      - DOMAIN=" new_domain;
                } else if (in_container && $0 ~ /TEMP_MAIL=/) {
                    print "      - TEMP_MAIL=" new_temp_mail;
                } else {
                    print $0;
                }
            }' "$COMPOSE_FILE" > "$TEMP_FILE"
            
            # 检查是否成功修改
            if [ $? -ne 0 ]; then
                echo -e "${RED}错误: 无法修改compose文件${NC}"
                rm "$TEMP_FILE"
                return 1
            fi
            
            # 应用更改
            cp "$TEMP_FILE" "$COMPOSE_FILE"
            rm "$TEMP_FILE"
            
            # 更新容器列表
            sed -i "${CONTAINER_NUM}s/: .*/: custom-$NEW_CUSTOM_DOMAIN-$CONTAINER_NUM/" "$CONTAINER_LIST_FILE"
        fi
    else
        # 标准账号容器（原有逻辑）
        echo -e "${YELLOW}请选择修改方式:${NC}"
        echo -e "1) 更改为另一个标准账号"
        echo -e "2) 更改为自建邮箱"
        read -p "请选择 (1/2): " EDIT_MODE
        
        if [ "$EDIT_MODE" == "1" ]; then
            # 输入新账号信息
            echo -e "${YELLOW}请输入新的账号信息，格式为: email----password----token----clientId${NC}"
            read -p "新账号信息: " NEW_EMAIL_EASY_CONFIG
            
            # 验证输入格式
            if [[ ! $NEW_EMAIL_EASY_CONFIG == *----*----* ]]; then
                echo -e "${RED}错误: 输入格式不正确，请使用 email----password----token----clientId 格式${NC}"
                return 1
            fi
            
            # 提取邮箱作为容器标识
            NEW_EMAIL_PART=$(echo $NEW_EMAIL_EASY_CONFIG | cut -d'-' -f1)
            
            # 临时文件保存修改
            TEMP_FILE=$(mktemp)
            
            # 修改compose文件中的环境变量
            awk -v container="$CONTAINER_NAME:" -v new_config="$NEW_EMAIL_EASY_CONFIG" '
            BEGIN { in_container = 0; env_found = 0; }
            {
                if ($0 ~ "  " container) { in_container = 1; }
                else if (in_container && $0 ~ /^  [a-zA-Z]/) { in_container = 0; }
                
                if (in_container && $0 ~ /EMAIL_EASY_CONFIG=/) {
                    print "      - EMAIL_EASY_CONFIG=" new_config;
                    env_found = 1;
                } else {
                    print $0;
                }
            }' "$COMPOSE_FILE" > "$TEMP_FILE"
            
            # 检查是否成功修改
            if [ $? -ne 0 ] || ! grep -q "$NEW_EMAIL_EASY_CONFIG" "$TEMP_FILE"; then
                echo -e "${RED}错误: 无法修改compose文件${NC}"
                rm "$TEMP_FILE"
                return 1
            fi
            
            # 应用更改
            cp "$TEMP_FILE" "$COMPOSE_FILE"
            rm "$TEMP_FILE"
            
            # 更新容器列表
            sed -i "${CONTAINER_NUM}s/: .*/: $NEW_EMAIL_PART/" "$CONTAINER_LIST_FILE"
        else
            # 更改为自建邮箱
            read -p "请输入域名 (例如: gpt7.link): " CUSTOM_DOMAIN
            read -p "请输入临时邮箱 (例如: <EMAIL>): " CUSTOM_TEMP_MAIL
            
            if [ -z "$CUSTOM_DOMAIN" ] || [ -z "$CUSTOM_TEMP_MAIL" ]; then
                echo -e "${RED}错误: 域名和临时邮箱不能为空${NC}"
                return 1
            fi
            
            # 临时文件保存修改
            TEMP_FILE=$(mktemp)
            
            # 修改compose文件，将标准账号配置替换为自建邮箱配置
            awk -v container="$CONTAINER_NAME:" -v domain="$CUSTOM_DOMAIN" -v temp_mail="$CUSTOM_TEMP_MAIL" '
            BEGIN { in_container = 0; }
            {
                if ($0 ~ "  " container) { in_container = 1; }
                else if (in_container && $0 ~ /^  [a-zA-Z]/) { in_container = 0; }
                
                if (in_container && $0 ~ /EMAIL_EASY_CONFIG=/) {
                    print "      - EMAIL_PROVIDER_TYPE=custom";
                    print "      - DOMAIN=" domain;
                    print "      - TEMP_MAIL=" temp_mail;
                } else {
                    print $0;
                }
            }' "$COMPOSE_FILE" > "$TEMP_FILE"
            
            # 检查是否成功修改
            if [ $? -ne 0 ]; then
                echo -e "${RED}错误: 无法修改compose文件${NC}"
                rm "$TEMP_FILE"
                return 1
            fi
            
            # 应用更改
            cp "$TEMP_FILE" "$COMPOSE_FILE"
            rm "$TEMP_FILE"
            
            # 更新容器列表
            CONTAINER_NUM_ONLY=$(echo $CONTAINER_NAME | sed 's/openai-regflow-//')
            sed -i "${CONTAINER_NUM}s/: .*/: custom-$CUSTOM_DOMAIN-$CONTAINER_NUM_ONLY/" "$CONTAINER_LIST_FILE"
        fi
    fi
    
    # 重启容器应用新配置
    echo -e "${YELLOW}正在重启容器 $CONTAINER_NAME 应用新配置...${NC}"
    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" restart $CONTAINER_NAME
    
    echo -e "${GREEN}容器 $CONTAINER_NAME 配置已更新${NC}"
    return 0
}

# 删除容器
remove_container() {
    # 显示容器列表
    show_containers
    
    # 选择要删除的容器
    echo -e "${YELLOW}请输入要删除的容器编号:${NC}"
    read -p "容器编号: " CONTAINER_NUM
    
    # 验证输入
    if ! [[ "$CONTAINER_NUM" =~ ^[0-9]+$ ]]; then
        echo -e "${RED}错误: 请输入有效的容器编号${NC}"
        return 1
    fi
    
    # 获取容器名称
    CONTAINER_NAME=$(sed -n "${CONTAINER_NUM}p" "$CONTAINER_LIST_FILE" | cut -d':' -f1 | xargs)
    
    if [ -z "$CONTAINER_NAME" ]; then
        echo -e "${RED}错误: 容器编号 $CONTAINER_NUM 不存在${NC}"
        return 1
    fi
    
    # 确认删除
    echo -e "${RED}确定要删除容器 $CONTAINER_NAME 吗?${NC}"
    read -p "确认删除? (y/n): " CONFIRM
    
    if [ "$CONFIRM" != "y" ]; then
        echo -e "${YELLOW}取消删除操作${NC}"
        return 0
    fi
    
    # 停止并删除容器
    echo -e "${YELLOW}正在停止并删除容器 $CONTAINER_NAME...${NC}"
    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" rm -sf $CONTAINER_NAME
    
    # 从compose文件中删除配置
    sed -i "/  $CONTAINER_NAME:/,/^$/d" "$COMPOSE_FILE"
    
    # 从容器列表中删除
    sed -i "${CONTAINER_NUM}d" "$CONTAINER_LIST_FILE"
    
    echo -e "${GREEN}容器 $CONTAINER_NAME 已成功删除${NC}"
    
    # 提示是否删除容器相关数据目录
    CONTAINER_NUM_ONLY=$(echo $CONTAINER_NAME | sed 's/openai-regflow-//')
    echo -e "${YELLOW}是否删除容器相关数据目录? (logs$CONTAINER_NUM_ONLY, screenshots$CONTAINER_NUM_ONLY, emails$CONTAINER_NUM_ONLY)${NC}"
    read -p "删除数据目录? (y/n): " DELETE_DATA
    
    if [ "$DELETE_DATA" == "y" ]; then
        rm -rf "$CONTAINER_INFO_DIR/logs$CONTAINER_NUM_ONLY" "$CONTAINER_INFO_DIR/screenshots$CONTAINER_NUM_ONLY" "$CONTAINER_INFO_DIR/emails$CONTAINER_NUM_ONLY"
        echo -e "${GREEN}容器数据目录已删除${NC}"
    else
        echo -e "${YELLOW}保留容器数据目录${NC}"
    fi
    
    return 0
}

# 删除所有容器
remove_all_containers() {
    # 显示容器列表
    show_containers
    
    # 检查是否有容器
    if [ ! -s "$CONTAINER_LIST_FILE" ]; then
        echo -e "${YELLOW}当前没有容器，无需删除${NC}"
        return 0
    fi
    
    # 确认删除全部
    echo -e "${RED}警告: 即将删除所有容器!${NC}"
    echo -e "${RED}此操作将停止并删除所有OpenAI-RegFlow容器${NC}"
    read -p "确认删除所有容器? (y/n): " CONFIRM
    
    if [ "$CONFIRM" != "y" ]; then
        echo -e "${YELLOW}取消删除操作${NC}"
        return 0
    fi
    
    # 确认是否删除数据目录
    read -p "是否同时删除所有容器的数据目录? (y/n): " DELETE_DATA
    
    # 停止所有容器
    echo -e "${YELLOW}正在停止所有容器...${NC}"
    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" down
    
    # 遍历容器列表进行删除
    while read -r LINE; do
        # 提取容器名称和编号
        CONTAINER_NAME=$(echo "$LINE" | cut -d':' -f1 | xargs)
        CONTAINER_NUM_ONLY=$(echo $CONTAINER_NAME | sed 's/openai-regflow-//')
        
        # 删除数据目录（如果选择了删除）
        if [ "$DELETE_DATA" == "y" ]; then
            rm -rf "$CONTAINER_INFO_DIR/logs$CONTAINER_NUM_ONLY" "$CONTAINER_INFO_DIR/screenshots$CONTAINER_NUM_ONLY" "$CONTAINER_INFO_DIR/emails$CONTAINER_NUM_ONLY"
            echo -e "${GREEN}已删除容器 $CONTAINER_NAME 的数据目录${NC}"
        fi
        
        echo -e "${GREEN}已处理容器 $CONTAINER_NAME${NC}"
    done < "$CONTAINER_LIST_FILE"
    
    # 清空docker-compose文件，只保留基本结构
    cat > "$COMPOSE_FILE" << EOF
version: '3.8'

services:
EOF
    
    # 清空容器列表文件
    > "$CONTAINER_LIST_FILE"
    
    echo -e "${GREEN}所有容器已成功删除!${NC}"
    if [ "$DELETE_DATA" == "y" ]; then
        echo -e "${GREEN}所有容器的数据目录已删除${NC}"
    else
        echo -e "${YELLOW}保留了所有容器的数据目录${NC}"
    fi
    
    return 0
}

# 查看日志
view_logs() {
    echo -e "${YELLOW}选择查看日志的方式:${NC}"
    echo -e "1) 查看特定容器的Docker日志"
    echo -e "2) 查看特定容器的combined.log文件"
    echo -e "3) 查看特定容器的error.log文件"
    echo -e "4) 查看所有容器的combined.log合并日志"
    echo -e "5) 查看项目根目录的combined.log文件"
    echo -e "6) 查看项目根目录的error.log文件"
    read -p "请选择 (1-6): " LOG_CHOICE
    
    case $LOG_CHOICE in
        1)
            # 显示容器列表
            show_containers
            echo -e "${YELLOW}请输入要查看日志的容器编号 (按Enter查看所有容器):${NC}"
            read -p "容器编号: " CONTAINER_NUM
            
            if [ -z "$CONTAINER_NUM" ]; then
                echo -e "${YELLOW}查看所有容器的日志...${NC}"
                $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" logs -f | less
            else
                # 验证输入
                if ! [[ "$CONTAINER_NUM" =~ ^[0-9]+$ ]]; then
                    echo -e "${RED}错误: 请输入有效的容器编号${NC}"
                    return 1
                fi
                
                # 获取容器名称
                CONTAINER_NAME=$(sed -n "${CONTAINER_NUM}p" "$CONTAINER_LIST_FILE" | cut -d':' -f1 | xargs)
                
                if [ -z "$CONTAINER_NAME" ]; then
                    echo -e "${RED}错误: 容器编号 $CONTAINER_NUM 不存在${NC}"
                    return 1
                fi
                
                echo -e "${YELLOW}查看容器 $CONTAINER_NAME 的日志...${NC}"
                docker logs -f $CONTAINER_NAME | less
            fi
            ;;
        2|3)
            # 显示容器列表
            show_containers
            echo -e "${YELLOW}请输入要查看日志的容器编号:${NC}"
            read -p "容器编号: " CONTAINER_NUM
            
            # 验证输入
            if ! [[ "$CONTAINER_NUM" =~ ^[0-9]+$ ]]; then
                echo -e "${RED}错误: 请输入有效的容器编号${NC}"
                return 1
            fi
            
            # 提取容器编号
            CONTAINER_ID=$(sed -n "${CONTAINER_NUM}p" "$CONTAINER_LIST_FILE" | cut -d'-' -f3 | cut -d':' -f1 | xargs)
            if [ -z "$CONTAINER_ID" ]; then
                CONTAINER_ID=$(echo $CONTAINER_NAME | sed 's/openai-regflow-//')
            fi
            
            # 选择日志文件
            if [ "$LOG_CHOICE" == "2" ]; then
                LOG_FILE="$CONTAINER_INFO_DIR/logs$CONTAINER_ID/combined.log"
                LOG_TYPE="combined.log"
            else
                LOG_FILE="$CONTAINER_INFO_DIR/logs$CONTAINER_ID/error.log"
                LOG_TYPE="error.log"
            fi
            
            # 检查日志文件是否存在
            if [ ! -f "$LOG_FILE" ]; then
                echo -e "${RED}错误: 日志文件 $LOG_FILE 不存在${NC}"
                return 1
            fi
            
            echo -e "${YELLOW}查看容器 #$CONTAINER_NUM 的 $LOG_TYPE 日志...${NC}"
            less "$LOG_FILE"
            ;;
        4)
            # 显示所有容器的combined.log合并日志
            echo -e "${YELLOW}查看所有容器的combined.log合并日志...${NC}"
            
            # 检查是否有容器
            if [ ! -s "$CONTAINER_LIST_FILE" ]; then
                echo -e "${RED}错误: 没有找到容器信息${NC}"
                return 1
            fi
            
            # 创建临时合并日志文件
            TEMP_MERGED_LOG=$(mktemp)
            
            # 合并所有容器的combined.log
            while read -r LINE; do
                # 提取容器名称和编号
                CONTAINER_NAME=$(echo "$LINE" | cut -d':' -f1 | xargs)
                CONTAINER_NUM_ONLY=$(echo $CONTAINER_NAME | sed 's/openai-regflow-//')
                EMAIL=$(echo "$LINE" | cut -d':' -f2- | xargs)
                
                # 构建日志文件路径
                CONTAINER_LOG="$CONTAINER_INFO_DIR/logs$CONTAINER_NUM_ONLY/combined.log"
                
                # 如果日志文件存在，添加到合并日志
                if [ -f "$CONTAINER_LOG" ]; then
                    echo -e "\n\n========== 容器 $CONTAINER_NAME ($EMAIL) 的日志 ==========" >> "$TEMP_MERGED_LOG"
                    cat "$CONTAINER_LOG" >> "$TEMP_MERGED_LOG"
                fi
            done < "$CONTAINER_LIST_FILE"
            
            # 显示合并日志
            less "$TEMP_MERGED_LOG"
            
            # 清理临时文件
            rm "$TEMP_MERGED_LOG"
            ;;
        5|6)
            # 选择项目根目录的日志文件
            if [ "$LOG_CHOICE" == "5" ]; then
                LOG_FILE="$PROJECT_ROOT/logs/combined.log"
                LOG_TYPE="combined.log"
            else
                LOG_FILE="$PROJECT_ROOT/logs/error.log"
                LOG_TYPE="error.log"
            fi
            
            # 检查日志文件是否存在
            if [ ! -f "$LOG_FILE" ]; then
                echo -e "${RED}错误: 项目根目录日志文件 $LOG_FILE 不存在${NC}"
                return 1
            fi
            
            echo -e "${YELLOW}查看项目根目录的 $LOG_TYPE 日志...${NC}"
            less "$LOG_FILE"
            ;;
        *)
            echo -e "${RED}错误: 无效的选择${NC}"
            return 1
            ;;
    esac
    
    return 0
}

# 重新装载.env文件并重启所有容器
reload_env_and_restart() {
    echo -e "${BLUE}=========================================${NC}"
    echo -e "${YELLOW}重新装载.env文件并重启所有容器${NC}"
    echo -e "${BLUE}=========================================${NC}"
    
    # 检查.env文件是否存在
    if [ ! -f "$PROJECT_ROOT/.env" ]; then
        echo -e "${RED}错误: 未找到.env文件 ($PROJECT_ROOT/.env)${NC}"
        echo -e "${YELLOW}请确保.env文件存在于项目根目录${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}检测到.env文件: $PROJECT_ROOT/.env${NC}"
    echo -e "${CYAN}注意: 此操作将停止所有容器，重新读取.env文件，然后重新启动容器${NC}"
    read -p "确定要继续吗？(y/n): " CONFIRM
    
    if [ "$CONFIRM" != "y" ]; then
        echo -e "${YELLOW}操作已取消${NC}"
        return 0
    fi
    
    # 显示当前运行的容器
    echo -e "${BLUE}当前运行的容器:${NC}"
    docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}"
    
    # 停止所有容器
    echo -e "${YELLOW}正在停止所有容器...${NC}"
    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" down
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 停止容器失败${NC}"
        return 1
    fi
    
    echo -e "${GREEN}所有容器已停止${NC}"
    sleep 2
    
    # 重新启动容器（这会重新读取.env文件）
    echo -e "${YELLOW}正在重新启动所有容器（重新装载.env文件）...${NC}"
    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" up -d
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}所有容器已重新启动，.env文件已重新装载${NC}"
        sleep 3
        
        # 显示重启后的状态
        echo -e "${BLUE}重启后的容器状态:${NC}"
        show_containers
    else
        echo -e "${RED}错误: 重新启动容器失败${NC}"
        echo -e "${YELLOW}请检查docker-compose.yml配置和.env文件内容${NC}"
        return 1
    fi
}

# 主菜单
main_menu() {
    echo -e "${BLUE}=========================================${NC}"
    echo -e "${GREEN}OpenAI-RegFlow 容器管理系统${NC}"
    echo -e "${BLUE}=========================================${NC}"
    echo -e "1) 查看所有容器状态"
    echo -e "2) 添加新容器"
    echo -e "3) 删除容器"
    echo -e "4) 删除所有容器"
    echo -e "5) 修改容器账号"
    echo -e "6) 启动所有容器"
    echo -e "7) 停止所有容器"
    echo -e "8) 重启所有容器"
    echo -e "9) 查看日志"
    echo -e "10) 更新环境和镜像"
    echo -e "11) 清理残留容器"
    echo -e "12) 重新装载.env文件并重启所有容器"
    echo -e "0) 退出"
    echo -e "${BLUE}=========================================${NC}"
    read -p "请选择操作 (0-12): " CHOICE
    
    case $CHOICE in
        0)
            echo -e "${GREEN}感谢使用，再见！${NC}"
            exit 0
            ;;
        1)
            show_containers
            ;;
        2)
            add_container
            ;;
        3)
            remove_container
            ;;
        4)
            remove_all_containers
            ;;
        5)
            edit_account
            ;;
        6)
            echo -e "${YELLOW}正在启动所有容器...${NC}"
            $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" up -d
            sleep 2
            show_containers
            ;;
        7)
            echo -e "${YELLOW}正在停止所有容器...${NC}"
            $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" down
            ;;
        8)
            echo -e "${YELLOW}正在重启所有容器...${NC}"
            $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" restart
            sleep 2
            show_containers
            ;;
        9)
            view_logs
            ;;
        10)
            echo -e "${YELLOW}更新环境并重建镜像...${NC}"
            echo -e "${RED}注意: 这将停止所有容器，重建镜像，然后重启所有容器${NC}"
            read -p "确定要继续吗？(y/n): " CONFIRM
            if [ "$CONFIRM" == "y" ]; then
                # 停止所有容器
                echo -e "${YELLOW}正在停止所有容器...${NC}"
                $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" down
                
                # 重建镜像
                echo -e "${YELLOW}正在删除旧镜像...${NC}"
                docker rmi openai-regflow -f
                
                echo -e "${YELLOW}正在构建新镜像...${NC}"
                cd "$PROJECT_ROOT" && docker build --no-cache -t openai-regflow .
                
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}镜像重建成功！${NC}"
                    
                    # 重启所有容器
                    echo -e "${YELLOW}正在重启所有容器...${NC}"
                    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" up -d
                    
                    # 显示状态
                    sleep 3
                    show_containers
                else
                    echo -e "${RED}错误: 镜像构建失败${NC}"
                fi
            else
                echo -e "${YELLOW}操作已取消${NC}"
            fi
            ;;
        11)
            echo -e "${RED}清理所有残留容器...${NC}"
            echo -e "${RED}注意: 这将强制删除所有 openai-regflow 相关容器${NC}"
            read -p "确定要继续吗？(y/n): " CONFIRM
            if [ "$CONFIRM" == "y" ]; then
                # 先尝试 docker compose down
                echo -e "${YELLOW}尝试使用 docker compose down...${NC}"
                $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" down
                
                # 然后强制删除所有相关容器
                echo -e "${YELLOW}强制删除所有 openai-regflow 容器...${NC}"
                docker ps -a | grep openai-regflow | awk '{print $1}' | xargs -r docker rm -f
                
                echo -e "${GREEN}清理完成${NC}"
                docker ps --filter "name=openai-regflow"
            else
                echo -e "${YELLOW}操作已取消${NC}"
            fi
            ;;
        12)
            reload_env_and_restart
            ;;
        *)
            echo -e "${RED}错误: 无效的选择${NC}"
            ;;
    esac
}

# 处理命令行参数
if [ $# -eq 0 ]; then
    # 交互式菜单模式
    while true; do
        main_menu
        echo ""
        read -p "按Enter键继续..."
        clear
    done
else
    # 命令行参数模式
    case "$1" in
        status)
            show_containers
            ;;
        start)
            echo -e "${YELLOW}启动所有容器...${NC}"
            $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" up -d
            sleep 2
            show_containers
            ;;
        stop)
            echo -e "${YELLOW}停止所有容器...${NC}"
            $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" down
            ;;
        restart)
            echo -e "${YELLOW}重启所有容器...${NC}"
            $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" restart
            sleep 2
            show_containers
            ;;
        logs)
            if [ -z "$2" ]; then
                # 进入交互式日志查看
                view_logs
            else
                # 直接查看指定容器的日志
                docker logs -f openai-regflow-$2 | less
            fi
            ;;
        add)
            add_container
            ;;
        remove)
            if [ -z "$2" ]; then
                remove_container
            else
                # 快速删除指定容器
                CONTAINER_NAME="openai-regflow-$2"
                echo -e "${YELLOW}正在停止并删除容器 $CONTAINER_NAME...${NC}"
                $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" rm -sf $CONTAINER_NAME
                # 从compose文件删除
                sed -i "/  $CONTAINER_NAME:/,/^$/d" "$COMPOSE_FILE"
                # 从容器列表删除
                sed -i "/$CONTAINER_NAME:/d" "$CONTAINER_LIST_FILE"
                echo -e "${GREEN}容器 $CONTAINER_NAME 已删除${NC}"
            fi
            ;;
        removeall)
            remove_all_containers
            ;;
        update)
            echo -e "${YELLOW}更新环境并重建镜像...${NC}"
            read -p "确定要更新吗？(y/n): " CONFIRM
            if [ "$CONFIRM" == "y" ]; then
                # 停止所有容器
                echo -e "${YELLOW}正在停止所有容器...${NC}"
                $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" down
                
                # 重建镜像
                echo -e "${YELLOW}正在删除旧镜像...${NC}"
                docker rmi openai-regflow -f
                
                echo -e "${YELLOW}正在构建新镜像...${NC}"
                cd "$PROJECT_ROOT" && docker build --no-cache -t openai-regflow .
                
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}镜像重建成功！${NC}"
                    
                    # 重启所有容器
                    echo -e "${YELLOW}正在重启所有容器...${NC}"
                    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" up -d
                    
                    # 显示状态
                    sleep 3
                    show_containers
                else
                    echo -e "${RED}错误: 镜像构建失败${NC}"
                fi
            fi
            ;;
        clean)
            echo -e "${RED}清理所有残留容器...${NC}"
            read -p "确定要清理吗？(y/n): " CONFIRM
            if [ "$CONFIRM" == "y" ]; then
                # 先尝试 docker compose down
                echo -e "${YELLOW}尝试使用 docker compose down...${NC}"
                $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" down
                
                # 然后强制删除所有相关容器
                echo -e "${YELLOW}强制删除所有 openai-regflow 容器...${NC}"
                docker ps -a | grep openai-regflow | awk '{print $1}' | xargs -r docker rm -f
                
                echo -e "${GREEN}清理完成${NC}"
                docker ps --filter "name=openai-regflow"
            fi
            ;;
        reload-env)
            reload_env_and_restart
            ;;
        *)
            echo -e "${YELLOW}OpenAI-RegFlow 管理脚本${NC}"
            echo -e "${BLUE}用法:${NC}"
            echo -e "  $0                  - 启动交互式菜单"
            echo -e "  $0 status           - 显示容器状态"
            echo -e "  $0 start            - 启动所有容器"
            echo -e "  $0 stop             - 停止所有容器"
            echo -e "  $0 restart          - 重启所有容器"
            echo -e "  $0 reload-env       - 重新装载.env文件并重启所有容器"
            echo -e "  $0 logs [容器编号]    - 查看容器日志"
            echo -e "  $0 add              - 添加新容器"
            echo -e "  $0 remove [容器编号]  - 删除指定容器"
            echo -e "  $0 removeall        - 删除所有容器"
            echo -e "  $0 update           - 更新环境并重建镜像"
            echo -e "  $0 clean            - 清理残留容器"
            exit 1
            ;;
    esac
fi 