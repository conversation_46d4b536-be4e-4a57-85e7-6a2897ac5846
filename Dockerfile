FROM node:22-bullseye

# 安装Puppeteer依赖
RUN apt-get update && apt-get install -y \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libasound2 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libnspr4 \
    libnss3 \
    fonts-liberation \
    libgtk-3-0 \
    libxtst6 \
    libdbus-glib-1-2 \
    ca-certificates \
    fonts-liberation \
    libnss3 \
    lsb-release \
    xdg-utils \
    wget \
    gnupg \
    curl \
    unzip \
    dumb-init

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制项目文件
COPY . .

# 编译TypeScript
RUN npm run build

# 设置环境变量
ENV NODE_ENV=production

# 添加额外的Puppeteer配置以确保在容器中正常运行
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium

# Puppeteer运行参数，禁用内容安全策略，降低内存限制
ENV PUPPETEER_ARGS="--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-dev-shm-usage --js-flags=--max-old-space-size=512"

# 安装Chromium
RUN apt-get update && apt-get install -y chromium

# 设置时区为亚洲/上海
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 使用dumb-init作为入口点，它会正确处理信号转发，有助于清理僵尸进程
ENTRYPOINT ["dumb-init", "--"]

# 运行应用
CMD ["npm", "start"] 