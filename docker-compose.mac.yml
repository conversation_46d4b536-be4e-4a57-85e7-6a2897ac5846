# Mac优化版 Docker Compose 配置文件
# 支持 Apple Silicon (M1/M2/M3) 和 Intel Mac
# 优化内存使用、CPU限制和性能表现

version: '3.8'

# 全局网络配置
networks:
  openai-regflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 全局卷配置
volumes:
  shared_cache:
    driver: local
  shared_logs:
    driver: local

services:
  # 示例容器配置 - 在实际使用中由脚本动态生成
  openai-regflow-example:
    image: openai-regflow:mac
    container_name: openai-regflow-example
    restart: unless-stopped
    
    # Mac平台特定配置
    platform: linux/arm64  # 或 linux/amd64 取决于Mac架构
    
    # Mac优化的资源限制
    mem_limit: 2g
    memswap_limit: 2g
    cpus: '2.0'
    cpu_count: 2
    
    # 网络配置
    networks:
      - openai-regflow-network
    
    # 卷挂载 - Mac优化路径
    volumes:
      # 只读配置文件
      - ./.env:/app/.env:ro,cached
      
      # 读写数据目录 - 使用delegated提升Mac性能
      - ./container_info/logs0:/app/logs:delegated
      - ./container_info/screenshots0:/app/screenshots:delegated
      - ./container_info/emails0:/app/emails:delegated
      
      # Mac Docker优化：共享缓存卷
      - shared_cache:/app/.cache
      
      # Mac系统集成
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      
    # Mac优化环境变量
    environment:
      # Node.js优化
      - NODE_ENV=production
      - NODE_OPTIONS=--max-old-space-size=1024 --max-semi-space-size=128
      
      # Puppeteer Mac优化配置
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_CACHE_DIR=/app/.cache/puppeteer
      
      # Mac Docker优化的Puppeteer参数
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process,VizDisplayCompositor --disable-site-isolation-trials --disable-dev-shm-usage --disable-gpu --disable-software-rasterizer --js-flags=--max-old-space-size=1024 --memory-pressure-off --max_old_space_size=1024
      
      # 显示相关
      - DISPLAY=:0
      
      # 时区设置
      - TZ=Asia/Shanghai
      
      # 账号配置 - 由脚本动态设置
      # - EMAIL_EASY_CONFIG=<EMAIL>----password----token----clientId
      # 或
      # - EMAIL_PROVIDER_TYPE=custom
      # - DOMAIN=your-domain.com
      # - TEMP_MAIL=<EMAIL>
    
      # 代理配置 - 可选，如需通过代理访问请取消注释并配置
      # - PROXY_ENABLED=true
      # - PROXY_SERVER=http://proxy-server:port  # 支持 http://ip:port, https://ip:port, socks5://ip:port
      # - PROXY_TYPE=http  # 代理类型: http, https, socks5
      # - PROXY_USERNAME=your_username  # 代理用户名（如果代理需要认证）
      # - PROXY_PASSWORD=your_password  # 代理密码（如果代理需要认证）
    
    # Mac安全优化
    cap_add:
      - SYS_ADMIN
    
    security_opt:
      - seccomp:unconfined
      - apparmor:unconfined
    
    # 共享内存配置 - Mac优化
    shm_size: 1g
    
    # 文件描述符限制
    ulimits:
      core:
        soft: 0
        hard: 0
      nofile:
        soft: 65536
        hard: 65536
      memlock:
        soft: -1
        hard: -1
    
    # Mac健康检查配置
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || nc -z localhost 3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # Mac日志配置优化
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
        compress: "true"
    
    # Mac DNS配置
    dns:
      - *******
      - *******
    
    # Mac端口映射 - 如果需要
    # ports:
    #   - "3000:3000"
    
    # Mac依赖服务 - 如果有其他服务
    # depends_on:
    #   - redis
    #   - mongodb

  # Redis缓存服务 - 可选
  # redis:
  #   image: redis:7-alpine
  #   container_name: openai-regflow-redis
  #   platform: linux/arm64  # 或 linux/amd64
  #   restart: unless-stopped
  #   mem_limit: 256m
  #   cpus: '0.5'
  #   networks:
  #     - openai-regflow-network
  #   volumes:
  #     - ./container_info/redis-data:/data:delegated
  #   command: redis-server --appendonly yes --maxmemory 200mb --maxmemory-policy allkeys-lru

# Mac性能监控配置 - 可选
# x-mac-monitoring: &mac-monitoring
#   logging:
#     driver: "json-file"
#     options:
#       max-size: "10m"
#       max-file: "1"
#   healthcheck:
#     interval: 60s
#     timeout: 5s
#     retries: 2 