version: '3.8'

services:
  openai-regflow:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: openai-regflow
    restart: unless-stopped
    volumes:
      - ./.env:/app/.env
      - ./logs:/app/logs
      - ./screenshots:/app/screenshots
      - ./emails:/app/emails
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-dev-shm-usage --js-flags=--max-old-space-size=512
      - EMAIL_EASY_CONFIG=<EMAIL>----hTyKkaDbLPK8----M.C558_SN1.0.U.-CqScthYyRHjQt!K0tmMRJst58kXVn5qNoxkkGUfjkJ!ZEyh!mHz3dTVh0MIl4dGiUHkzbsM6bm3iE8Q!P2KNC6NBAyEbB5uZWL!KrtxNrKS!zfv0UVM17xs4rOaKjpP2dyaeE8nmgoRe4v3qxu04EJ5TKeMzqKMWwZrrZUHlQPWX*rd9mb9aSCvpsfxL9oe7vEBTDcMxAx95flm!u6nXnTTiW2tdGsyaN2ALuPJMsP4cyBALze2Z0WkCRvxr9SYEpOk6enMEK7M5DZRCZQYOA3WiuTodcz1NCKaKlLvdW1d0WkS94EVTh5jaUvH5h!nCzt5ZEO8GNtqx2ZLlVJWxagielyO*YD*pWvUWv8IbNg5F2wOAm3n0z7sUrS!LZgeM7N*1elHJ0PBWXP87M!L6ob2h*tAMQarRzktVxsoI3qf9----9e5f94bc-e8a4-4e73-b8be-63364c29d753    
      # 代理配置 - 可选，如需通过代理访问请取消注释并配置
      # - PROXY_ENABLED=true
      # - PROXY_SERVER=http://proxy-server:port  # 支持 http://ip:port, https://ip:port, socks5://ip:port
      # - PROXY_TYPE=http  # 代理类型: http, https, socks5
      # - PROXY_USERNAME=your_username  # 代理用户名（如果代理需要认证）
      # - PROXY_PASSWORD=your_password  # 代理密码（如果代理需要认证）
    cap_add:
      - SYS_ADMIN
    # 由于OpenAI站点的安全限制，建议使用特权模式
    privileged: true
    shm_size: 512mb  # 降低共享内存，适应低内存环境
    ulimits:
      core:
        soft: 0
        hard: 0
      nofile:
        soft: 65536
        hard: 65536
