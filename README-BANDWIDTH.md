# 流量优化配置说明

## 概述

为了减少代理流量消耗，本系统新增了流量优化功能，可以阻止加载图片、字体、CSS样式表等非必需资源。

## 配置方法

### 环境变量配置

设置以下环境变量来启用流量优化：

```bash
OPTIMIZE_BANDWIDTH=true
```

### Docker Compose 配置

在 `docker-compose.yml` 或 `docker-compose.mac.yml` 文件中，已默认启用流量优化：

```yaml
environment:
  - OPTIMIZE_BANDWIDTH=true
```

## 被阻止的资源类型

当启用流量优化时，以下类型的资源将被阻止加载：

- **image**: 图片文件 (jpg, png, gif, svg, webp 等)
- **font**: 字体文件 (woff, woff2, ttf, otf 等)
- **media**: 媒体文件 (mp4, mp3, avi 等)
- **stylesheet**: CSS样式表文件
- **other**: 其他非必需资源

## 保留的资源类型

以下资源类型仍会正常加载，确保功能正常：

- **document**: HTML文档
- **script**: JavaScript脚本
- **xhr**: AJAX请求
- **fetch**: Fetch API请求

## 预期效果

- **流量节省**: 可节省 60-80% 的网络流量
- **加载速度**: 页面加载速度提升
- **成本降低**: 显著降低代理使用成本

## 注意事项

1. 启用流量优化后，网页可能显示不完整（无图片、无样式）
2. 但核心功能（注册、验证等）不受影响
3. 如果遇到问题，可以设置 `OPTIMIZE_BANDWIDTH=false` 来禁用

## 使用示例

### 直接运行
```bash
export OPTIMIZE_BANDWIDTH=true
npm start
```

### Docker 运行
```bash
docker run -e OPTIMIZE_BANDWIDTH=true openai-regflow
```

### 临时禁用流量优化
如果需要调试或遇到问题，可以临时禁用：

```bash
# 修改 docker-compose.yml
- OPTIMIZE_BANDWIDTH=false

# 或者在启动脚本中设置
export OPTIMIZE_BANDWIDTH=false
``` 