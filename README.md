# OpenAI-RegFlow

An automated OpenAI account registration system with multi-provider email verification.

## Features

- **Multiple Email Provider Support**:
    - Microsoft (Outlook/Hotmail)
    - Microsoft Alias (无限别名注册，如 <EMAIL>)
    - Gmail
    - Self-hosted email servers
- **Browser Automation**:
    - Uses Puppeteer with stealth mode to avoid detection
    - Automatic form filling and navigation
    - Screenshot capture for debugging
- **Verification Code Handling**:
    - Automatic retrieval of verification codes from emails
    - Support for various email verification patterns
    - 自动清理邮箱，获取验证码后删除所有邮件，避免重复获取
- **Bulk Registration**:
    - Process multiple registrations from a config file
    - Configurable delays between registrations
- **API Key Management**:
    - Automatic extraction of API keys after registration
    - Save API keys to file for later use

## Prerequisites

- Node.js 16+
- npm or yarn

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/openai-regflow.git
   cd openai-regflow
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env` file with your configuration:
   ```
   # General settings
   NODE_ENV=development
   LOG_LEVEL=info
   BROWSER_HEADLESS=false
   
   # Email settings (Microsoft example)
   EMAIL_TYPE=microsoft
   EMAIL_ADDRESS=<EMAIL>
   CLIENT_ID=your-client-id
   REFRESH_TOKEN=your-refresh-token
   
   # Registration settings
   RETRY_ATTEMPTS=3
   DELAY_BETWEEN_ATTEMPTS=10000
   ```

## Usage

### Configuration via Environment Variables

The easiest way to use OpenAI-RegFlow is by setting the appropriate environment variables in a `.env` file:

1. Copy the `.env.example` file to `.env`:
   ```
   cp .env.example .env
   ```

2. Edit the `.env` file to set your email provider type and credentials:
   ```
   # Set email provider type (microsoft, gmail, or custom)
   EMAIL_PROVIDER_TYPE=microsoft
   
   # Set email credentials based on provider type
   EMAIL_ADDRESS=<EMAIL>
   CLIENT_ID=your-client-id
   REFRESH_TOKEN=your-refresh-token
   
   # For Gmail, also set:
   # CLIENT_SECRET=your-client-secret
   
   # For custom email, set:
   # EMAIL_PASSWORD=your-password
   # EMAIL_HOST=mail.your-domain.com
   # EMAIL_PORT=993
   ```

3. Run the registration:
   ```
   npm start -- register
   ```

### Single Registration

To register a single account using your `.env` file settings:

```
npm start -- register
```

All configuration is automatically loaded from your `.env` file.

### Batch Registration

Create a JSON file with multiple registration configurations:

```json
[
  {
    "emailConfig": {
      "type": "microsoft",
      "email": "<EMAIL>",
      "clientId": "client-id-1",
      "refreshToken": "refresh-token-1"
    },
    "userData": {
      "firstName": "John",
      "lastName": "Doe",
      "dateOfBirth": {
        "day": "15",
        "month": "5",
        "year": "1985"
      },
      "country": "US"
    },
    "password": "StrongPassword123!"
  },
  {
    "emailConfig": {
      "type": "gmail",
      "email": "<EMAIL>",
      "clientId": "client-id-2",
      "clientSecret": "client-secret-2",
      "refreshToken": "refresh-token-2"
    }
  }
]
```

Then run:

```
npm start -- batch registrations.json
```

### Verification Code Only

If you just want to retrieve a verification code from an email:

```
npm start -- verify
```

## Email Provider Configuration

### Microsoft Email

```
EMAIL_TYPE=microsoft
EMAIL_ADDRESS=<EMAIL>
CLIENT_ID=your-client-id
REFRESH_TOKEN=your-refresh-token
```

### Microsoft Email Alias

使用微软邮箱别名功能可以无限创建邮箱别名（<EMAIL>），并在同一主邮箱接收验证码。
适用于 Hotmail、Outlook、Live 等微软邮箱。

```
EMAIL_PROVIDER_TYPE=microsoft-alias
EMAIL_ADDRESS=<EMAIL>
CLIENT_ID=your-client-id
REFRESH_TOKEN=your-refresh-token
USE_MICROSOFT_ALIAS=true
MICROSOFT_ALIAS_PREFIX=gpt
MICROSOFT_ALIAS_RANDOM_LENGTH=5
```

别名生成规则：
- 原始邮箱：<EMAIL>
- 生成别名：<EMAIL>
- 验证码仍然发送到原始邮箱（<EMAIL>）

### Gmail

```
EMAIL_TYPE=gmail
EMAIL_ADDRESS=<EMAIL>
CLIENT_ID=your-client-id
CLIENT_SECRET=your-client-secret
REFRESH_TOKEN=your-refresh-token
```

### Custom/Self-hosted Email

```
EMAIL_TYPE=custom
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your-password
EMAIL_HOST=mail.your-domain.com
EMAIL_PORT=993
```

## Obtaining Microsoft Refresh Token

To obtain a refresh token for Microsoft emails:

1. Register an application in the [Microsoft Azure Portal](https://portal.azure.com/)
2. Set the redirect URI to `http://localhost`
3. Grant the following permissions:
    - `offline_access`
    - `IMAP.AccessAsUser.All`
    - `POP.AccessAsUser.All`
4. Use the OAuth 2.0 authentication flow to get a refresh token

## Obtaining Gmail Refresh Token

To obtain a refresh token for Gmail:

1. Create a project in the [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the Gmail API
3. Create OAuth 2.0 credentials
4. Set the redirect URI to `http://localhost`
5. Grant the following scopes:
    - `https://mail.google.com/`
    - `https://www.googleapis.com/auth/gmail.readonly`
6. Use the OAuth 2.0 authentication flow to get a refresh token

## Programmatic Usage

You can also use the library programmatically in your own projects:

```typescript
import { 
  registerAccount, 
  EmailProviderType,
  EmailConfig 
} from 'openai-regflow';

const emailConfig: EmailConfig = {
  type: EmailProviderType.MICROSOFT,
  email: '<EMAIL>',
  clientId: 'your-client-id',
  refreshToken: 'your-refresh-token'
};

// Register with auto-generated user data and password
registerAccount(emailConfig)
  .then(() => console.log('Registration completed'))
  .catch(error => console.error('Registration failed:', error));
```

## License

MIT

## Disclaimer

This tool is meant for educational purposes and legitimate use cases only. Always ensure you comply with OpenAI's terms of service when creating accounts.

# OpenAI-RegFlow 批量注册工具

OpenAI-RegFlow是一个自动化的OpenAI账号注册工具，可以单个或批量注册OpenAI账号并获取API密钥。

## 主要功能

- **单个账号注册**：使用环境变量中的配置注册单个OpenAI账号
- **批量账号注册**：从文件读取多个账号凭证，并并行注册多个OpenAI账号
- **验证码获取**：独立的验证码获取功能，可以从邮箱中提取OpenAI发送的验证码
  - **邮箱自动清理**：获取验证码后自动删除所有邮件，防止下次获取时出现混乱
- **多邮箱提供商支持**：支持Microsoft(Outlook/Hotmail)、Gmail和自定义邮箱服务器

## 新增功能：通过配置文件指定注册模式

最新版本允许通过配置文件（.env）指定注册模式，不需要使用命令行参数。只需设置以下环境变量：

```
# 注册模式 - single(单个) 或 batch(批量)
REGISTRATION_MODE=batch

# 批量模式设置（仅当 REGISTRATION_MODE=batch 时使用）
REGISTRATION_CREDENTIALS_FILE=accounts/accounts.txt
REGISTRATION_OUTPUT_FILE=api_keys.txt
REGISTRATION_MAX_CONCURRENT=2
```

设置好配置文件后，直接运行以下命令即可按照配置文件中指定的模式进行注册：

```bash
npm start
```

## 批量注册功能

批量注册可以从文本文件读取多个微软账号凭证，并同时开启多个浏览器进行并行注册，提高注册效率。

### 凭证文件格式

批量注册使用的凭证文件格式为：
```
邮箱地址----密码----刷新令牌----客户端ID
```

例如：
```
<EMAIL>----Password123----M.R3_BAY.0.U.-longtokenstring----clientid
```

### 微软邮箱别名批量注册

使用微软邮箱别名功能可以通过一个主邮箱无限创建邮箱别名，并在同一邮箱接收验证码。
设置以下环境变量启用微软邮箱别名批量注册：

```
# 注册模式设置
REGISTRATION_MODE=batch
# 邮箱类型设置为microsoft-alias
EMAIL_PROVIDER_TYPE=microsoft-alias
# 主邮箱地址
EMAIL_ADDRESS=<EMAIL>
# OAuth认证信息
CLIENT_ID=your-client-id
REFRESH_TOKEN=your-refresh-token
# 批量注册数量
REGISTRATION_BATCH_COUNT=10
# 别名设置
MICROSOFT_ALIAS_PREFIX=gpt
MICROSOFT_ALIAS_RANDOM_LENGTH=5
# 输出文件
REGISTRATION_OUTPUT_FILE=api_keys.txt
```

批量注册过程中会自动生成不同的别名邮箱，如 `<EMAIL>`，所有验证码都会发送到主邮箱 `<EMAIL>`。

### 使用方法

#### 方法1：通过配置文件（推荐）
1. 编辑`.env`文件，设置批量模式：
   ```
   REGISTRATION_MODE=batch
   REGISTRATION_CREDENTIALS_FILE=accounts/accounts.txt
   REGISTRATION_OUTPUT_FILE=api_keys.txt
   REGISTRATION_MAX_CONCURRENT=2
   ```
2. 运行程序：
   ```bash
   npm start
   ```

#### 方法2：通过命令行参数
```bash
npm start -- batch --credentials accounts.txt --output api_keys.txt --concurrent 2
```

参数说明：
- `--credentials`：指定凭证文件路径（默认：accounts.txt）
- `--output`：指定输出API密钥的文件路径（默认：api_keys.txt）
- `--concurrent`：指定最大并行注册数量（默认：1）

### 输出格式

成功注册的API密钥将保存到指定的输出文件，格式为：
```
邮箱地址----API密钥
```

## 命令行选项

虽然推荐使用配置文件设置注册模式，但仍然保留了命令行选项，用于特殊需求：

```
OpenAI-RegFlow - 自动化OpenAI账号注册工具

用法:
  npm start                       使用配置文件中的设置进行注册
  npm start -- [command] [options]  使用命令行参数

命令:
  verify                获取邮箱验证码(不注册)
  multi <config.json>   从JSON配置文件处理多个注册
  batch [options]       从凭证文件批量注册账号
  help                  显示帮助信息

批量模式选项:
  --credentials <file>  凭证文件 (默认: accounts.txt)
  --output <file>       输出API密钥文件 (默认: api_keys.txt)
  --concurrent <num>    最大并发注册数 (默认: 1)
```

## 环境配置

使用前需配置.env文件或环境变量，配置示例见`.env.example`文件：

```
# 注册模式 - single(单个) 或 batch(批量)
REGISTRATION_MODE=single
# 批量模式设置（仅当 REGISTRATION_MODE=batch 时使用）
REGISTRATION_CREDENTIALS_FILE=accounts/accounts.txt
REGISTRATION_OUTPUT_FILE=api_keys.txt
REGISTRATION_MAX_CONCURRENT=2

# 邮箱提供商设置 (microsoft, gmail, 或 custom)
EMAIL_PROVIDER_TYPE=microsoft
EMAIL_ADDRESS=<EMAIL>
# ...其他配置...