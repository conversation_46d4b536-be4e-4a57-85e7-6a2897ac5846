# Environment settings
NODE_ENV=development
LOG_LEVEL=info

# Registration mode - single or batch
REGISTRATION_MODE=single
# Batch mode settings (only used when REGISTRATION_MODE=batch)
REGISTRATION_CREDENTIALS_FILE=accounts/accounts.txt
REGISTRATION_OUTPUT_FILE=api_keys.txt
REGISTRATION_MAX_CONCURRENT=2

# Email provider settings (microsoft, gmail, or custom)
EMAIL_PROVIDER_TYPE=microsoft
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your-password  # Used for custom email provider
CLIENT_ID=your-oauth-client-id
CLIENT_SECRET=your-oauth-client-secret  # Required for Gmail
REFRESH_TOKEN=your-oauth-refresh-token
EMAIL_HOST=mail.your-domain.com  # Used for custom email provider
EMAIL_PORT=993  # Used for custom email provider
EMAIL_PROTOCOL=IMAP  # IMAP or POP3
DOMAIN=mjapi.xyz  # Your Cloudflare domain
TEMP_MAIL=<EMAIL>  # tempmail.plus generated temporary email
TEMP_MAIL_EPIN=28t8qPEXrG.t4Cg  # tempmail.plus epin
TEMP_MAIL_EXT=@mailto.plus  # tempmail.plus extension

EMAIL_EASY_CONFIG=EMAIL_ADDRESS----EMAIL_PASSWORD----REFRESH_TOKEN----CLIENT_ID

# Browser settings
BROWSER_HEADLESS=false
SCREENSHOTS_DIR=screenshots
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36

# Proxy settings (optional, for avoiding IP restrictions)
# Set PROXY_ENABLED=true to enable proxy support
PROXY_ENABLED=false
# Proxy server address (e.g., http://proxy.example.com:8080, socks5://127.0.0.1:1080)
PROXY_SERVER=
# Proxy type: http, https, or socks5
PROXY_TYPE=http
# Proxy authentication (optional)
PROXY_USERNAME=
PROXY_PASSWORD=

OPTIMIZE_BANDWIDTH=true

# Registration settings
RETRY_ATTEMPTS=3
DELAY_BETWEEN_ATTEMPTS=10000
VERIFICATION_TIMEOUT=300000
MAX_VERIFICATION_ATTEMPTS=10
DELAY_BETWEEN_REGISTRATIONS=60000
DEFAULT_COUNTRY=US
BATCH_REGISTRATION_COUNT=10  # 批量注册的账号数量

# User data (optional, will be randomly generated if not provided)
USER_FIRST_NAME=John
USER_LAST_NAME=Doe
USER_DOB_DAY=01
USER_DOB_MONTH=01
USER_DOB_YEAR=1990
USER_COUNTRY=US

# Output settings
SAVE_KEYS_TO_FILE=true
KEYS_FILE=api_keys.json

# Debug settings - 调试信息保存控制 (默认不保存截图和邮件以节省磁盘空间)
SAVE_SCREENSHOTS=false   # 是否保存截图文件 (默认: false，设置为true启用)
SAVE_EMAILS=false        # 是否保存邮件内容 (默认: false，设置为true启用)
SAVE_DEBUG_LOGS=true     # 是否保存调试日志到文件 (默认: true，设置为false禁用)

# Microsoft-specific settings (optional, only if you need to override defaults)
# MICROSOFT_POP3_SERVER=outlook.office365.com
# MICROSOFT_POP3_PORT=995

# Custom email-specific settings (optional, only if you need to override defaults)
# CUSTOM_EMAIL_USE_TLS=true

# System Submission Settings
# 如果不需要系统提交功能，可以注释掉这些配置
SYSTEM_SUBMISSION_URL=http://your-system-url/api/channel/
SYSTEM_SUBMISSION_AUTHORIZATION=your-authorization-token
SYSTEM_SUBMISSION_USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36
SYSTEM_SUBMISSION_CONTENT_TYPE=application/json
SYSTEM_SUBMISSION_ACCEPT=application/json, text/plain, */*

# Channel Config
CHANNEL_NAME=微软自建注册
CHANNEL_TYPE=1
CHANNEL_BILLING_TYPE=1
CHANNEL_GROUP=default
CHANNEL_MODELS=gpt-4o-mini,gpt-4.1-mini,gpt-4.1-nano,gpt-4.1-nano-2025-04-14,gpt-4.1-mini-2025-04-14,gpt-4o-mini-2024-07-18
CHANNEL_GROUP_ID=11
CHANNEL_MIN_REQUEST_TOKEN_COUNT=1
CHANNEL_MAX_REQUEST_TOKEN_COUNT=16384
REQUEST_TOKEN_LIMIT_ENABLED=false

# 微软邮箱别名配置
# 将 EMAIL_PROVIDER_TYPE 设置为 microsoft-alias 以启用微软邮箱别名功能
# EMAIL_PROVIDER_TYPE=microsoft-alias
# 启用微软邮箱别名
USE_MICROSOFT_ALIAS=true
# 别名前缀（可选）
MICROSOFT_ALIAS_PREFIX=gpt
# 别名随机字符串长度
MICROSOFT_ALIAS_RANDOM_LENGTH=5
