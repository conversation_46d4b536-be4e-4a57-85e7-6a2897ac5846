# 调试信息保存控制

为了帮助用户节省磁盘空间，我们添加了控制调试信息保存的配置选项。

## 🎯 功能概述

系统默认会保存以下调试信息：
- **截图文件** - 注册过程中的页面截图 (通常每个几MB)
- **邮件内容** - 验证邮件的完整内容
- **调试日志** - 详细的运行日志文件

这些文件对调试很有用，但会占用大量磁盘空间。现在你可以选择性地禁用它们。

## ⚙️ 配置选项

在 `.env` 文件中添加以下配置：

```bash
# Debug settings - 调试信息保存控制
SAVE_SCREENSHOTS=false   # 是否保存截图文件 (默认: true)
SAVE_EMAILS=false        # 是否保存邮件内容 (默认: true)  
SAVE_DEBUG_LOGS=false    # 是否保存调试日志到文件 (默认: true)
```

## 🚀 快速设置

### 方法一：使用脚本 (推荐)

**禁用所有调试信息保存：**
```bash
./disable-debug-saves.sh
```

**重新启用所有调试信息保存：**
```bash
./enable-debug-saves.sh
```

### 方法二：手动编辑

编辑 `.env` 文件，设置对应的环境变量为 `false` 或 `true`。

## 📊 磁盘空间节省效果

| 功能 | 文件类型 | 大概大小 | 节省效果 |
|------|----------|----------|----------|
| 截图保存 | PNG图片 | 2-5MB/张 | 高 |
| 邮件保存 | JSON文本 | 1-10KB/封 | 中 |
| 日志保存 | 日志文件 | 持续增长 | 中 |

## ⚠️ 重要说明

### 禁用后的影响

1. **截图 (SAVE_SCREENSHOTS=false)**
   - ✅ 不影响程序正常运行
   - ❌ 无法查看注册过程的页面截图
   - ❌ 出错时无法通过截图定位问题

2. **邮件保存 (SAVE_EMAILS=false)**
   - ✅ 不影响邮件读取和验证码提取
   - ❌ 无法查看完整的邮件内容历史
   - ❌ 无法手动检查邮件内容

3. **调试日志 (SAVE_DEBUG_LOGS=false)**
   - ✅ 控制台仍会显示日志信息
   - ❌ 无法保存日志到文件进行后续分析
   - ❌ 程序重启后无法查看历史日志

### 推荐设置

**生产环境 (节省空间)：**
```bash
SAVE_SCREENSHOTS=false
SAVE_EMAILS=false
SAVE_DEBUG_LOGS=false
```

**开发/调试环境：**
```bash
SAVE_SCREENSHOTS=true
SAVE_EMAILS=true
SAVE_DEBUG_LOGS=true
```

**平衡设置 (推荐)：**
```bash
SAVE_SCREENSHOTS=false    # 截图占用空间最大，优先禁用
SAVE_EMAILS=false         # 邮件内容通常不需要长期保存
SAVE_DEBUG_LOGS=true      # 保留日志便于问题排查
```

## 🔄 配置生效

修改配置后需要重启应用程序：

```bash
# 如果使用 Docker
docker-compose restart

# 如果直接运行
# 停止程序后重新启动
```

## 📁 文件位置

- **截图文件**: `screenshots/` 目录
- **邮件文件**: `emails/` 目录  
- **日志文件**: `logs/` 目录

## 🛠️ 故障排除

如果遇到问题，可以临时启用所有调试信息：

```bash
./enable-debug-saves.sh
```

然后重新运行程序，查看详细的调试信息来定位问题。
