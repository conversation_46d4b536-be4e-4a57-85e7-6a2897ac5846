{"name": "openai-regflow", "version": "1.0.0", "description": "Automated OpenAI account registration system with multi-provider email verification", "main": "dist/index.js", "scripts": {"build": "NODE_OPTIONS=--max-old-space-size=4096 tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "lint": "eslint . --ext .ts"}, "keywords": ["openai", "automation", "registration", "email", "verification"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.8.4", "buffer": "^6.0.3", "dotenv": "^16.3.1", "googleapis": "^129.0.0", "imap": "^0.8.19", "mailparser": "^3.7.2", "nodemailer": "^6.9.8", "puppeteer": "^23.7.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "winston": "^3.11.0"}, "devDependencies": {"@types/imap": "^0.8.39", "@types/jest": "^29.5.11", "@types/mailparser": "^3.4.5", "@types/node": "^20.10.6", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "eslint": "^8.56.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}