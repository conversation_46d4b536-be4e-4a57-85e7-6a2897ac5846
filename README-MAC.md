# OpenAI-RegFlow Mac优化版

## 🍎 专为Mac系统优化的OpenAI账号注册自动化工具

本版本专门针对macOS系统进行优化，支持Apple Silicon (M1/M2/M3)和Intel Mac，提供更好的性能和稳定性。

## 📋 系统要求

### 基础要求
- **操作系统**: macOS 11.0 (Big Sur) 或更高版本
- **内存**: 最少8GB RAM (推荐16GB或更多)
- **存储**: 至少5GB可用空间
- **处理器**: Apple Silicon (M1/M2/M3) 或 Intel x86_64

### Mac架构支持
- ✅ **Apple Silicon (M1/M2/M3)**: 原生支持，性能最佳
- ✅ **Intel Mac**: 完全兼容，运行稳定

### 内存推荐配置
| Mac内存 | 推荐容器数 | 性能表现 |
|---------|------------|----------|
| 8GB     | 2-4个      | 基础使用 |
| 16GB    | 4-8个      | 推荐配置 |
| 32GB+   | 8-16个     | 高性能  |

## 🛠 安装配置

### 1. 安装Docker Desktop

```bash
# 方法1: 使用Homebrew安装 (推荐)
brew install --cask docker

# 方法2: 从官网下载
# 访问: https://www.docker.com/products/docker-desktop
```

### 2. Docker Desktop配置

打开Docker Desktop，进入 **Settings**:

#### 资源配置 (Resources)
```
CPU: 4-8核 (根据你的Mac配置)
Memory: 4-8GB (推荐至少分配系统内存的50%)
Swap: 2GB
Disk image size: 至少20GB
```

#### 功能设置 (Features)
- ✅ 启用 "Use Virtualization framework"
- ✅ 启用 "Use Rosetta for x86/amd64 emulation" (仅Apple Silicon)

### 3. 项目配置

```bash
# 克隆项目
git clone <your-repo-url>
cd OpenAI-RegFlow

# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env  # 或使用你喜欢的编辑器
```

## 🌐 代理配置 (Mac版)

### 代理配置说明

为了避免IP限制，Mac版本支持通过代理服务器访问OpenAI服务。

### 支持的代理类型
- **HTTP代理**: `http://proxy-server:port`
- **HTTPS代理**: `https://proxy-server:port`
- **SOCKS5代理**: `socks5://proxy-server:port`

### Mac代理配置方法

#### 方法1: 编辑启动脚本
编辑 `start_many_mac.sh` 脚本，在容器配置中取消代理配置注释：

```bash
# 找到以下行并取消注释
# - PROXY_ENABLED=true
# - PROXY_SERVER=http://proxy-server:port
# - PROXY_TYPE=http
# - PROXY_USERNAME=username  # 可选
# - PROXY_PASSWORD=password  # 可选
```

#### 方法2: 环境变量配置
创建或编辑 `.env` 文件：

```bash
# Mac代理配置
PROXY_ENABLED=true
PROXY_SERVER=http://your-proxy-server:8080
PROXY_TYPE=http
PROXY_USERNAME=your_username
PROXY_PASSWORD=your_password
```

### Mac代理配置示例

#### Apple Silicon Mac + HTTP代理
```bash
PROXY_ENABLED=true
PROXY_SERVER=http://127.0.0.1:8080
PROXY_TYPE=http
```

#### Intel Mac + SOCKS5代理
```bash
PROXY_ENABLED=true
PROXY_SERVER=socks5://proxy.example.com:1080
PROXY_TYPE=socks5
PROXY_USERNAME=myuser
PROXY_PASSWORD=mypass
```

### Mac代理验证

启动容器后检查代理是否生效：

```bash
# 查看容器日志
docker logs openai-regflow-0 | grep -i proxy

# 应该看到类似输出：
# [浏览器] 使用代理: http://proxy-server:8080
# 启用代理配置: http://proxy-server:8080
```

### Mac代理故障排除

#### 常见问题
1. **代理连接失败**
   ```bash
   # 检查代理服务器是否可达
   curl -x http://proxy-server:8080 http://www.google.com
   ```

2. **Apple Silicon兼容性**
   ```bash
   # 确保代理服务器支持ARM64架构
   # 或在Docker Desktop中启用Rosetta模式
   ```

3. **网络切换问题**
   ```bash
   # Mac网络切换后重启容器
   docker compose -f container_info/docker-compose.yml restart
   ```

## 🚀 快速开始

### 使用Mac优化启动脚本

```bash
# 给脚本执行权限
chmod +x start_many_mac.sh

# 启动Mac优化版
./start_many_mac.sh
```

### 输入方式选择

脚本提供4种输入方式：

1. **逐个输入** - 适合少量账号
2. **文件批量导入** - 适合大量账号 (推荐)
3. **自建邮箱模式** - 使用自定义域名
4. **Mac快速配置** - 使用示例数据测试

### 文件批量导入配置

创建 `accounts.txt` 文件：

```txt
# OpenAI-RegFlow Mac版批量账号导入文件
# 标准账号格式: email----password----token----clientId
<EMAIL>----password123----token123----clientId123
<EMAIL>----password456----token456----clientId456

# 自建邮箱格式: 域名----临时邮箱
<EMAIL>
```

## 🔧 Mac专用命令

### 基础操作

```bash
# 查看系统和Docker信息
./start_many_mac.sh

# 更新镜像和容器
./start_many_mac.sh update

# 查看容器状态
docker ps --filter "name=openai-regflow"

# 查看容器日志
docker logs -f openai-regflow-0

# 查看资源使用情况
docker stats
```

### 性能监控

```bash
# 检查Mac系统资源
top -o cpu

# 检查内存使用
memory_pressure

# 检查Docker Desktop资源使用
# 打开Docker Desktop Dashboard
```

### 容器管理

```bash
# 停止所有容器
docker compose -f container_info/docker-compose.yml down

# 启动所有容器
docker compose -f container_info/docker-compose.yml up -d

# 重启特定容器
docker restart openai-regflow-0

# 删除所有容器和镜像 (谨慎操作)
docker compose -f container_info/docker-compose.yml down
docker rmi openai-regflow:mac
```

## ⚡ Mac性能优化

### Apple Silicon (M1/M2/M3) 优化

- **原生ARM64支持**: 使用 `linux/arm64` 平台镜像
- **内存优化**: 每容器限制1-2GB内存
- **CPU限制**: 智能分配CPU核心
- **缓存优化**: 使用共享卷减少I/O开销

### Intel Mac优化

- **x86_64兼容**: 使用 `linux/amd64` 平台镜像  
- **内存管理**: 适当增加内存分配
- **编译优化**: 针对Intel架构优化编译参数

### 通用Mac优化

- **文件同步**: 使用 `delegated` 模式提升I/O性能
- **共享内存**: 优化shm_size配置
- **日志管理**: 限制日志文件大小防止磁盘占满
- **健康检查**: 智能容器健康监控

## 🐛 常见问题解决

### Docker相关问题

**问题**: Docker Desktop无法启动
```bash
# 解决方案
sudo launchctl stop com.docker.vmnetd
sudo launchctl start com.docker.vmnetd
```

**问题**: 镜像构建失败
```bash
# 清理Docker缓存
docker system prune -a
docker builder prune -f

# 重新构建
./start_many_mac.sh
# 选择 "3) 强制重建"
```

### 内存问题

**问题**: 容器内存不足
```bash
# 检查Docker Desktop内存分配
# Settings -> Resources -> Memory

# 或减少同时运行的容器数量
docker ps
docker stop <container-names>
```

### 性能问题

**问题**: 容器运行缓慢
```bash
# 检查系统负载
top -o cpu

# 检查Docker资源
docker stats

# 优化方案
# 1. 增加Docker Desktop内存分配
# 2. 减少同时运行的容器数量
# 3. 关闭其他耗内存应用
```

### 网络问题

**问题**: 网络连接失败
```bash
# 重置Docker网络
docker network prune

# 检查DNS设置
nslookup google.com
```

## 📊 性能基准

### Apple Silicon Mac (M2, 16GB)
- **推荐容器数**: 6-8个
- **内存使用**: 每容器1-1.5GB
- **CPU使用**: 每容器0.5-1.0核心
- **启动时间**: 2-3分钟

### Intel Mac (i7, 16GB)  
- **推荐容器数**: 4-6个
- **内存使用**: 每容器1.5-2GB
- **CPU使用**: 每容器1.0-1.5核心
- **启动时间**: 3-5分钟

## 🔐 安全建议

### Mac系统安全
- 确保macOS系统为最新版本
- 开启FileVault磁盘加密
- 使用强密码保护用户账户

### Docker安全
- 定期更新Docker Desktop
- 不要在生产环境使用特权模式
- 限制容器资源使用

### 数据安全
- 定期备份重要数据
- 使用`.env`文件保护敏感信息
- 不要将敏感信息提交到版本控制

## 📞 技术支持

### 日志文件位置
```
./container_info/logs*/          # 应用日志
./container_info/screenshots*/   # 截图文件
./container_info/emails*/        # 邮件文件
```

### 调试命令
```bash
# 进入容器调试
docker exec -it openai-regflow-0 /bin/bash

# 查看详细日志
docker logs --details openai-regflow-0

# 检查容器配置
docker inspect openai-regflow-0
```

### 获取帮助
- 查看容器日志找到错误信息
- 检查Mac系统资源使用情况
- 确认Docker Desktop配置正确
- 提交问题时请附上系统信息和错误日志

## 🎯 最佳实践

1. **资源监控**: 定期检查系统资源使用情况
2. **渐进部署**: 从少量容器开始，逐步增加
3. **定期更新**: 保持Docker和镜像为最新版本
4. **备份重要数据**: 定期备份配置和生成的数据
5. **性能调优**: 根据Mac配置调整容器数量和资源限制

## 📝 更新日志

### v2.0-mac
- ✅ 完全支持Apple Silicon (M1/M2/M3)
- ✅ Intel Mac兼容性优化
- ✅ 内存和CPU智能分配
- ✅ Mac文件系统优化
- ✅ 改进的错误处理和日志
- ✅ 代理配置支持 (新增)

---

**注意**: 本工具仅供学习和研究使用，请遵守相关服务条款和法律法规。
