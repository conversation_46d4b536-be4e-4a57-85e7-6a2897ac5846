# OpenAI-RegFlow Docker 部署指南

此文档说明如何使用 Docker 在 Linux 服务器上部署 OpenAI-RegFlow。

## 更新说明 (v1.0.1)

- **内容安全策略(CSP)修复**: 修复了OpenAI网站的内容安全策略限制导致的JavaScript错误
- **增强的浏览器稳定性**: 增加共享内存和文件描述符限制，提高浏览器稳定性
- **改进的Docker配置**: 优化容器运行环境，减少崩溃概率
- **兼容新版Docker Compose**: 更新命令格式为 `docker compose` (无连字符)

## 前提条件

- 安装 Docker 和 Docker Compose
- 准备好 `.env` 文件（包含所有必要的配置）

## 安装 Docker 和 Docker Compose

如果您的服务器尚未安装 Docker 和 Docker Compose，请按照以下步骤进行安装：

```bash
# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装 Docker Compose 插件 (新版Docker内置)
apt-get update
apt-get install -y docker-compose-plugin
```

## 部署步骤

1. 将项目代码复制到服务器上的目录中

2. 在项目根目录创建 `.env` 文件，并填入必要的配置信息

3. 使用提供的启动脚本来部署和管理服务：

```bash
# 添加执行权限
chmod +x start.sh

# 运行脚本
./start.sh
```

4. 从脚本菜单中选择 "1. 构建并启动服务"

## 手动部署

如果不使用启动脚本，也可以手动执行以下命令：

```bash
# 构建并启动服务
docker compose up -d --build

# 查看日志
docker compose logs -f

# 停止服务
docker compose down
```

## 目录结构

- `.env` - 环境变量配置文件
- `logs/` - 日志输出目录
- `data/` - 数据存储目录

## 常见错误和解决方案

### 内容安全策略(CSP)错误

如果在日志中看到类似以下错误：

```
[浏览器] 页面JavaScript错误: Error [EvalError]: Refused to evaluate a string as JavaScript because 'unsafe-eval' is not an allowed source of script in the following Content Security Policy directive...
```

这个问题在v1.0.1版本中已修复。如果仍然出现，请确保:

1. 使用最新版本的Docker配置文件
2. 重新构建Docker镜像: `docker compose up -d --build`

### 浏览器崩溃或无响应

如果浏览器经常崩溃，可以尝试以下解决方案：

1. 增加共享内存大小：
   ```yaml
   shm_size: 8gb  # 增加到8GB
   ```

2. 确保服务器有足够的内存和CPU资源

### Docker Compose命令问题

如果遇到`docker-compose: command not found`错误，请确保:

1. 使用新版命令格式：`docker compose`(无连字符)
2. 或安装旧版Docker Compose：`apt-get install docker-compose`

## 注意事项

- 默认配置了亚洲/上海时区，如需更改，请修改 Dockerfile 中的 `TZ` 环境变量
- 容器设置为自动重启（`restart: unless-stopped`），服务器重启后会自动恢复运行
- 如果OpenAI更改了其网站结构或安全策略，可能需要更新程序代码 