#!/bin/bash

# OpenAI-RegFlow Mac优化版多容器启动脚本
# 支持 Apple Silicon (M1/M2/M3) 和 Intel Mac
# 作者: AI Assistant
# 版本: 2.0-mac

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'  
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Mac系统检测
detect_mac_system() {
    echo -e "${BLUE}=========================================${NC}"
    echo -e "${GREEN}检测Mac系统环境...${NC}"
    echo -e "${BLUE}=========================================${NC}"
    
    # 检测Mac版本
    MAC_VERSION=$(sw_vers -productVersion)
    MAC_BUILD=$(sw_vers -buildVersion)
    
    # 检测CPU架构
    CPU_ARCH=$(uname -m)
    if [[ "$CPU_ARCH" == "arm64" ]]; then
        PLATFORM="Apple Silicon (M1/M2/M3)"
        DOCKER_PLATFORM="linux/arm64"
    else
        PLATFORM="Intel"
        DOCKER_PLATFORM="linux/amd64"
    fi
    
    # 检测可用内存
    TOTAL_MEM=$(sysctl -n hw.memsize)
    TOTAL_MEM_GB=$((TOTAL_MEM / 1024 / 1024 / 1024))
    
    echo -e "${CYAN}Mac系统信息:${NC}"
    echo -e "  版本: macOS $MAC_VERSION (Build: $MAC_BUILD)"
    echo -e "  架构: $CPU_ARCH ($PLATFORM)"
    echo -e "  内存: ${TOTAL_MEM_GB}GB"
    echo -e "  Docker平台: $DOCKER_PLATFORM"
    
    # 根据内存大小推荐容器数量
    if [ $TOTAL_MEM_GB -ge 16 ]; then
        MAX_RECOMMENDED_CONTAINERS=8
    elif [ $TOTAL_MEM_GB -ge 8 ]; then
        MAX_RECOMMENDED_CONTAINERS=4
    else
        MAX_RECOMMENDED_CONTAINERS=2
    fi
    
    echo -e "  推荐最大容器数: ${GREEN}$MAX_RECOMMENDED_CONTAINERS${NC}"
}

# 检测Docker环境
detect_docker_compose() {
    echo -e "\n${YELLOW}检测Docker环境...${NC}"
    
    # 检查Docker是否运行
    if ! docker info >/dev/null 2>&1; then
        echo -e "${RED}错误: Docker未运行或未安装${NC}"
        echo -e "${YELLOW}请确保Docker Desktop已启动${NC}"
        echo -e "${YELLOW}安装Docker Desktop: https://www.docker.com/products/docker-desktop${NC}"
        exit 1
    fi
    
    # 检查Docker版本
    DOCKER_VERSION=$(docker --version | awk '{print $3}' | sed 's/,//')
    echo -e "${GREEN}Docker版本: $DOCKER_VERSION${NC}"
    
    # 优先检查 docker compose (v2)
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        COMPOSE_VERSION=$(docker compose version --short)
    elif command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
        COMPOSE_VERSION=$(docker-compose --version | awk '{print $3}' | sed 's/,//')
    else
        echo -e "${RED}错误: 未找到Docker Compose${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Docker Compose版本: $COMPOSE_VERSION${NC}"
    echo -e "${GREEN}使用命令: $DOCKER_COMPOSE_CMD${NC}"
    
    return 0
}

# Mac优化的镜像构建
build_mac_optimized_image() {
    echo -e "\n${YELLOW}构建Mac优化镜像...${NC}"
    echo -e "${CYAN}目标平台: $DOCKER_PLATFORM${NC}"
    
    # 检查是否使用Mac专用Dockerfile
    if [ -f "Dockerfile.mac" ]; then
        DOCKERFILE="Dockerfile.mac"
        echo -e "${GREEN}使用Mac优化Dockerfile: $DOCKERFILE${NC}"
    else
        DOCKERFILE="Dockerfile"
        echo -e "${YELLOW}使用标准Dockerfile: $DOCKERFILE${NC}"
    fi
    
    # Mac优化构建参数
    BUILD_ARGS=(
        "--platform" "$DOCKER_PLATFORM"
        "--build-arg" "BUILDPLATFORM=$DOCKER_PLATFORM"
        "--build-arg" "TARGETPLATFORM=$DOCKER_PLATFORM"
        "--build-arg" "TARGETARCH=$CPU_ARCH"
        "--tag" "openai-regflow:mac"
        "--file" "$DOCKERFILE"
        "."
    )
    
    # 根据可用内存调整构建参数
    if [ $TOTAL_MEM_GB -ge 16 ]; then
        BUILD_ARGS+=("--memory" "4g")
    elif [ $TOTAL_MEM_GB -ge 8 ]; then
        BUILD_ARGS+=("--memory" "2g")
    else
        BUILD_ARGS+=("--memory" "1g")
    fi
    
    echo -e "${YELLOW}开始构建镜像 (预计需要3-10分钟)...${NC}"
    if docker build "${BUILD_ARGS[@]}"; then
        echo -e "${GREEN}Mac优化镜像构建成功!${NC}"
        return 0
    else
        echo -e "${RED}镜像构建失败${NC}"
        return 1
    fi
}

# 检查是否需要重建镜像
check_rebuild_needed() {
    echo -e "\n${YELLOW}镜像管理选项:${NC}"
    echo -e "1) 使用现有镜像 (如果存在)"
    echo -e "2) 重建镜像 (推荐，确保最新优化)"
    echo -e "3) 强制重建 (清除缓存重建)"
    read -p "请选择 (1/2/3): " REBUILD_CHOICE

    case $REBUILD_CHOICE in
        2)
            return 0  # 需要重建
            ;;
        3)
            return 2  # 强制重建
            ;;
        *)
            return 1  # 不重建
            ;;
    esac
}

# 重建镜像函数
rebuild_image() {
    local FORCE_REBUILD=$1
    
    if [ "$FORCE_REBUILD" == "force" ]; then
        echo -e "${YELLOW}强制重建镜像（清除缓存）...${NC}"
        docker rmi openai-regflow:mac -f 2>/dev/null || true
        docker builder prune -f
        build_mac_optimized_image
    else
        echo -e "${YELLOW}重建镜像...${NC}"
        build_mac_optimized_image
    fi
}

# Mac优化的容器配置生成
generate_mac_container_config() {
    local CONTAINER_NAME=$1
    local CONTAINER_COUNT=$2
    local CONFIG_LINE=$3
    local CONFIG_TYPE=$4
    
    # Mac Docker性能优化配置
    local MAC_DOCKER_OPTS=""
    
    # 根据Mac架构优化
    if [[ "$CPU_ARCH" == "arm64" ]]; then
        # Apple Silicon优化
        MAC_DOCKER_OPTS="--platform linux/arm64"
        MEMORY_LIMIT="1g"
        CPU_LIMIT="1.0"
    else
        # Intel Mac优化  
        MAC_DOCKER_OPTS="--platform linux/amd64"
        MEMORY_LIMIT="1.5g"
        CPU_LIMIT="1.5"
    fi
    
    # 根据系统内存调整资源限制
    if [ $TOTAL_MEM_GB -ge 16 ]; then
        MEMORY_LIMIT="2g"
        CPU_LIMIT="2.0"
    elif [ $TOTAL_MEM_GB -ge 8 ]; then
        MEMORY_LIMIT="1.5g"
        CPU_LIMIT="1.5"
    else
        MEMORY_LIMIT="1g"
        CPU_LIMIT="1.0"
    fi
    
    # 生成容器配置
    cat >> $MAIN_COMPOSE_FILE << EOF
  $CONTAINER_NAME:
    image: openai-regflow:mac
    container_name: $CONTAINER_NAME
    restart: unless-stopped
    platform: $DOCKER_PLATFORM
    mem_limit: $MEMORY_LIMIT
    cpus: $CPU_LIMIT
    volumes:
      - ../.env:/app/.env:ro
      - ./logs$CONTAINER_COUNT:/app/logs
      - ./screenshots$CONTAINER_COUNT:/app/screenshots
      - ./emails$CONTAINER_COUNT:/app/emails
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    environment:
      - NODE_ENV=production
      - PUPPETEER_SKIP_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-web-security --disable-features=IsolateOrigins,site-per-process,VizDisplayCompositor --disable-site-isolation-trials --disable-dev-shm-usage --disable-gpu --disable-software-rasterizer --js-flags=--max-old-space-size=1024 --memory-pressure-off --max_old_space_size=1024
      - NODE_OPTIONS=--max-old-space-size=1024 --max-semi-space-size=128
      - DISPLAY=:0
      # Mac代理配置 - 如需要可以在脚本中设置这些环境变量
      # - PROXY_ENABLED=true
      # - PROXY_SERVER=http://proxy-server:port  # 支持 http://ip:port, https://ip:port, socks5://ip:port
      # - PROXY_TYPE=http  # 代理类型: http, https, socks5
      # - PROXY_USERNAME=username  # 代理用户名（如果代理需要认证）
      # - PROXY_PASSWORD=password  # 代理密码（如果代理需要认证）
EOF

    # 根据配置类型添加特定环境变量
    if [ "$CONFIG_TYPE" == "standard" ]; then
        cat >> $MAIN_COMPOSE_FILE << EOF
      - EMAIL_EASY_CONFIG=$CONFIG_LINE
EOF
    elif [ "$CONFIG_TYPE" == "custom" ]; then
        local CUSTOM_DOMAIN=$(echo "$CONFIG_LINE" | awk -F'----' '{print $1}')
        local CUSTOM_TEMP_MAIL=$(echo "$CONFIG_LINE" | awk -F'----' '{print $2}')
        cat >> $MAIN_COMPOSE_FILE << EOF
      - EMAIL_PROVIDER_TYPE=custom
      - DOMAIN=$CUSTOM_DOMAIN
      - TEMP_MAIL=$CUSTOM_TEMP_MAIL
EOF
    fi

    # Mac优化配置
    cat >> $MAIN_COMPOSE_FILE << EOF
    cap_add:
      - SYS_ADMIN
    security_opt:
      - seccomp:unconfined
    shm_size: 1g
    ulimits:
      core:
        soft: 0
        hard: 0
      nofile:
        soft: 65536
        hard: 65536
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

EOF
}

# 主程序开始
echo -e "${PURPLE}=========================================${NC}"
echo -e "${GREEN}  OpenAI-RegFlow Mac优化版启动器  ${NC}"
echo -e "${PURPLE}=========================================${NC}"

# 检测Mac系统
detect_mac_system

# 检测Docker
detect_docker_compose

# 检查更新模式
if [ "$1" == "update" ]; then
    echo -e "${BLUE}=========================================${NC}"
    echo -e "${GREEN}OpenAI-RegFlow Mac更新模式${NC}"
    echo -e "${BLUE}=========================================${NC}"

    # 检查compose文件是否存在
    MAIN_COMPOSE_FILE="container_info/docker-compose.yml"
    if [ ! -f "$MAIN_COMPOSE_FILE" ]; then
        echo -e "${RED}错误: 未找到 docker-compose.yml 文件${NC}"
        echo -e "${YELLOW}请先运行脚本创建容器${NC}"
        exit 1
    fi

    # 停止所有容器
    echo -e "${YELLOW}正在停止所有容器...${NC}"
    $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE down

    # 重建镜像
    rebuild_image force

    # 重启所有容器
    echo -e "${YELLOW}正在重启所有容器...${NC}"
    $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d

    # 显示容器状态
    sleep 5
    echo -e "\n${BLUE}当前容器状态:${NC}"
    docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

    echo -e "\n${GREEN}Mac优化更新完成！所有容器已使用新镜像重启${NC}"
    exit 0
fi

# 检查并构建镜像
if docker image inspect openai-regflow:mac >/dev/null 2>&1; then
    echo -e "${GREEN}Mac优化镜像已存在${NC}"
    
    # 直接调用函数，不使用命令替换
    check_rebuild_needed
    REBUILD_RESULT=$?
    
    case $REBUILD_RESULT in
        0)
            rebuild_image
            ;;
        2)
            rebuild_image force
            ;;
        *)
            echo -e "${GREEN}使用现有镜像${NC}"
            ;;
    esac
else
    echo -e "${YELLOW}首次构建Mac优化镜像...${NC}"
    if ! build_mac_optimized_image; then
        echo -e "${RED}镜像构建失败，请检查Docker配置和网络连接${NC}"
        exit 1
    fi
fi

# 创建必要目录
echo -e "${YELLOW}创建必要目录...${NC}"
mkdir -p container_info/{logs,screenshots,emails}

# 显示欢迎信息
echo -e "${BLUE}=========================================${NC}"
echo -e "${GREEN}OpenAI-RegFlow Mac多容器管理${NC}"
echo -e "${BLUE}=========================================${NC}"

# 创建主compose文件
MAIN_COMPOSE_FILE="container_info/docker-compose.yml"

# 初始化主compose文件
cat > $MAIN_COMPOSE_FILE << EOF
version: '3.8'

services:
EOF

# 容器计数器
CONTAINER_COUNT=0

# 添加Mac专用批量输入功能提示
echo -e "\n${YELLOW}选择输入方式:${NC}"
echo -e "1) 逐个输入账号信息"
echo -e "2) 从文件批量导入 (推荐)"
echo -e "3) 批量创建自建邮箱账号"
echo -e "4) Mac专用快速配置 (使用示例数据)"
read -p "请选择 (1/2/3/4): " INPUT_MODE

# Mac专用快速配置
if [ "$INPUT_MODE" == "4" ]; then
    echo -e "\n${YELLOW}Mac专用快速配置模式${NC}"
    echo -e "${CYAN}将创建2个示例容器进行测试${NC}"
    
    # 创建示例配置
    EXAMPLE_CONFIGS=(
        "<EMAIL>----password123----token123----clientId123"
        "<EMAIL>----password456----token456----clientId456"
    )
    
    for i in "${!EXAMPLE_CONFIGS[@]}"; do
        CONFIG_LINE="${EXAMPLE_CONFIGS[$i]}"
        CONTAINER_NAME="openai-regflow-$CONTAINER_COUNT"
        
        # 创建容器目录
        mkdir -p container_info/logs$CONTAINER_COUNT container_info/screenshots$CONTAINER_COUNT container_info/emails$CONTAINER_COUNT
        
        # 生成配置
        generate_mac_container_config "$CONTAINER_NAME" "$CONTAINER_COUNT" "$CONFIG_LINE" "standard"
        
        # 记录容器信息
        EMAIL_PART=$(echo "$CONFIG_LINE" | awk -F'----' '{print $1}')
        echo "$CONTAINER_NAME: $EMAIL_PART (Mac示例)" >> container_info/container_list.txt
        
        echo -e "${GREEN}已添加Mac示例容器: $CONTAINER_NAME${NC}"
        CONTAINER_COUNT=$((CONTAINER_COUNT+1))
    done
    
    # 启动容器
    echo -e "\n${YELLOW}正在启动Mac示例容器...${NC}"
    $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d
    
elif [ "$INPUT_MODE" == "2" ]; then
    # 文件批量导入 (使用原有逻辑，但使用Mac优化配置)
    DEFAULT_FILE="accounts.txt"

    if [ ! -f "$DEFAULT_FILE" ]; then
        echo -e "${YELLOW}创建Mac优化的样板文件...${NC}"
        cat > $DEFAULT_FILE << 'ACCOUNTS_EOF'
# OpenAI-RegFlow Mac版批量账号导入文件
# 格式: email----password----token----clientId
# 以 # 开头的行将被忽略

# Mac测试示例账号：
<EMAIL>----password123----token123----clientId123
<EMAIL>----password456----token456----clientId456

# 自建邮箱示例（使用2个----分隔）：
# <EMAIL>

# Mac性能提示：
# 1. 建议同时运行容器数不超过 $MAX_RECOMMENDED_CONTAINERS 个
# 2. Apple Silicon Mac性能更佳
# 3. 确保Docker Desktop分配足够内存
ACCOUNTS_EOF
        echo -e "${GREEN}已创建Mac优化样板文件: $DEFAULT_FILE${NC}"
        echo -e "${YELLOW}请编辑该文件后重新运行脚本${NC}"
        exit 0
    fi

    echo -e "${YELLOW}可用的导入文件:${NC}"
    echo -e "1) 默认文件: $DEFAULT_FILE"  
    echo -e "2) 自定义文件路径"
    read -p "请选择 (1/2): " FILE_CHOICE

    if [ "$FILE_CHOICE" == "1" ]; then
        FILE_PATH="$DEFAULT_FILE"
    else
        read -p "请输入文件路径: " FILE_PATH
    fi

    if [ ! -f "$FILE_PATH" ]; then
        echo -e "${RED}错误: 文件不存在${NC}"
        exit 1
    fi

    # 统计并处理配置
    TOTAL_ACCOUNTS=$(grep -v '^#' "$FILE_PATH" | grep -v '^[[:space:]]*$' | wc -l | xargs)
    echo -e "${BLUE}找到 $TOTAL_ACCOUNTS 个配置${NC}"
    
    # 检查是否超过推荐数量
    if [ "$TOTAL_ACCOUNTS" -gt "$MAX_RECOMMENDED_CONTAINERS" ]; then
        echo -e "${YELLOW}警告: 配置数量($TOTAL_ACCOUNTS)超过推荐值($MAX_RECOMMENDED_CONTAINERS)${NC}"
        echo -e "${YELLOW}可能影响Mac性能，建议分批运行${NC}"
        read -p "是否继续? (y/n): " CONTINUE_CHOICE
        if [ "$CONTINUE_CHOICE" != "y" ] && [ "$CONTINUE_CHOICE" != "Y" ]; then
            exit 0
        fi
    fi

    # 处理每个配置
    PROCESSED_COUNT=0
    while IFS= read -r CONFIG_LINE || [ -n "$CONFIG_LINE" ]; do
        [[ -z "$CONFIG_LINE" || "$CONFIG_LINE" =~ ^# ]] && continue

        CONFIG_LINE=$(echo "$CONFIG_LINE" | xargs)
        [[ ! $CONFIG_LINE == *----* ]] && continue

        PROCESSED_COUNT=$((PROCESSED_COUNT+1))
        echo -e "\n${BLUE}处理第 $PROCESSED_COUNT/$TOTAL_ACCOUNTS 个配置${NC}"

        SEGMENT_COUNT=$(echo "$CONFIG_LINE" | grep -o "----" | wc -l | xargs)
        CONTAINER_NAME="openai-regflow-$CONTAINER_COUNT"

        mkdir -p container_info/logs$CONTAINER_COUNT container_info/screenshots$CONTAINER_COUNT container_info/emails$CONTAINER_COUNT

        if [ $SEGMENT_COUNT -eq 3 ]; then
            # 标准账号
            EMAIL_PART=$(echo "$CONFIG_LINE" | awk -F'----' '{print $1}')
            echo -e "${GREEN}Mac配置标准账号: $EMAIL_PART${NC}"
            
            generate_mac_container_config "$CONTAINER_NAME" "$CONTAINER_COUNT" "$CONFIG_LINE" "standard"
            echo "$CONTAINER_NAME: $EMAIL_PART (Mac)" >> container_info/container_list.txt
            
        elif [ $SEGMENT_COUNT -eq 1 ]; then
            # 自建邮箱
            CUSTOM_DOMAIN=$(echo "$CONFIG_LINE" | awk -F'----' '{print $1}')
            CUSTOM_TEMP_MAIL=$(echo "$CONFIG_LINE" | awk -F'----' '{print $2}')
            echo -e "${GREEN}Mac配置自建邮箱: $CUSTOM_DOMAIN -> $CUSTOM_TEMP_MAIL${NC}"
            
            generate_mac_container_config "$CONTAINER_NAME" "$CONTAINER_COUNT" "$CONFIG_LINE" "custom"
            echo "$CONTAINER_NAME: custom-$CUSTOM_DOMAIN-$CONTAINER_COUNT (Mac)" >> container_info/container_list.txt
        else
            echo -e "${RED}警告: 无法识别配置格式，跳过: $CONFIG_LINE${NC}"
            continue
        fi

        echo -e "${GREEN}已添加Mac容器配置: $CONTAINER_NAME${NC}"
        CONTAINER_COUNT=$((CONTAINER_COUNT+1))
    done < "$FILE_PATH"

    # 批量启动
    if [ $CONTAINER_COUNT -gt 0 ]; then
        echo -e "\n${YELLOW}正在批量启动Mac优化容器...${NC}"
        $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d
    fi

else
    # 逐个输入模式
    echo -e "\n${YELLOW}选择账号类型:${NC}"
    echo -e "1) 标准账号 (email----password----token----clientId)"
    echo -e "2) 自建邮箱 (需指定域名和临时邮箱)"
    read -p "请选择 (1/2): " ACCOUNT_TYPE
    
    if [ "$ACCOUNT_TYPE" == "2" ]; then
        # 自建邮箱模式
        echo -e "${YELLOW}Mac优化自建邮箱输入格式: 域名----临时邮箱----其他信息 (最后一段将被忽略)${NC}"
        echo -e "${YELLOW}示例: <EMAIL>----107.175.63.168-RackNerd${NC}"
        
        while true; do
            echo -e "\n${YELLOW}Mac容器 #$((CONTAINER_COUNT+1)) 配置${NC}"
            echo -e "${YELLOW}请输入自建邮箱配置信息，或输入 'q' 退出${NC}"
            read -p "配置信息: " CUSTOM_CONFIG
            
            # 检查是否退出
            if [[ "$CUSTOM_CONFIG" == "q" || "$CUSTOM_CONFIG" == "Q" ]]; then
                break
            fi
            
            # 验证输入格式
            if [[ ! $CUSTOM_CONFIG == *----* ]]; then
                echo -e "${RED}错误: 输入格式不正确，需要包含 ---- 分隔符${NC}"
                continue
            fi
            
            # 拆分配置信息
            CUSTOM_DOMAIN=$(echo "$CUSTOM_CONFIG" | awk -F'----' '{print $1}')
            CUSTOM_TEMP_MAIL=$(echo "$CUSTOM_CONFIG" | awk -F'----' '{print $2}')
            
            if [ -z "$CUSTOM_DOMAIN" ] || [ -z "$CUSTOM_TEMP_MAIL" ]; then
                echo -e "${RED}错误: 域名和临时邮箱不能为空，跳过此容器${NC}"
                continue
            fi
            
            echo -e "${GREEN}Mac解析结果 - 域名: $CUSTOM_DOMAIN, 临时邮箱: $CUSTOM_TEMP_MAIL${NC}"
            
            CONTAINER_NAME="openai-regflow-$CONTAINER_COUNT"
            
            # 创建容器专用目录
            mkdir -p container_info/logs$CONTAINER_COUNT container_info/screenshots$CONTAINER_COUNT container_info/emails$CONTAINER_COUNT
            
            # 使用Mac优化配置生成容器
            generate_mac_container_config "$CONTAINER_NAME" "$CONTAINER_COUNT" "$CUSTOM_CONFIG" "custom"
            
            # 记录容器信息
            echo "$CONTAINER_NAME: custom-$CUSTOM_DOMAIN-$CONTAINER_COUNT (Mac)" >> container_info/container_list.txt
            
            # 启动当前容器
            echo -e "${YELLOW}正在启动Mac容器 $CONTAINER_NAME...${NC}"
            $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d $CONTAINER_NAME
            
            # 检查容器是否成功启动
            sleep 2
            if docker ps | grep -q "$CONTAINER_NAME"; then
                echo -e "${GREEN}Mac容器 $CONTAINER_NAME 启动成功!${NC}"
                
                # 增加计数器
                CONTAINER_COUNT=$((CONTAINER_COUNT+1))
                
                # 显示当前容器列表
                echo -e "${BLUE}当前Mac容器列表:${NC}"
                docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
                
                echo -e "${GREEN}Mac容器 #$CONTAINER_COUNT 已添加并启动${NC}"
            else
                echo -e "${RED}错误: Mac容器 $CONTAINER_NAME 启动失败${NC}"
                # 删除失败的容器配置 - 需要删除Mac优化配置的整个section
                # Mac配置比较复杂，简单删除可能不完整，建议重新生成compose文件
                echo -e "${YELLOW}正在清理失败的容器配置...${NC}"
                $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE down $CONTAINER_NAME 2>/dev/null || true
                docker rm -f $CONTAINER_NAME 2>/dev/null || true
                # 删除容器记录
                sed -i "/$CONTAINER_NAME:/d" container_info/container_list.txt 2>/dev/null || true
            fi
        done
    else
        # 标准账号模式 - Mac优化版本
        while true; do
            echo -e "\n${YELLOW}Mac容器 #$((CONTAINER_COUNT+1)) 配置${NC}"
            echo -e "${YELLOW}请输入账号信息，格式为: email----password----token----clientId${NC}"
            echo -e "${YELLOW}或者输入 'q' 退出${NC}"
            read -p "账号信息: " EMAIL_EASY_CONFIG
            
            # 检查是否退出
            if [[ "$EMAIL_EASY_CONFIG" == "q" || "$EMAIL_EASY_CONFIG" == "Q" ]]; then
                break
            fi
            
            # 验证输入格式
            if [[ ! $EMAIL_EASY_CONFIG == *----*----* ]]; then
                echo -e "${RED}错误: 输入格式不正确，请使用 email----password----token----clientId 格式${NC}"
                continue
            fi
            
            # 提取邮箱作为容器标识
            EMAIL_PART=$(echo "$EMAIL_EASY_CONFIG" | awk -F'----' '{print $1}')
            CONTAINER_NAME="openai-regflow-$CONTAINER_COUNT"
            
            # 创建容器专用目录
            mkdir -p container_info/logs$CONTAINER_COUNT container_info/screenshots$CONTAINER_COUNT container_info/emails$CONTAINER_COUNT
            
            # 使用Mac优化配置生成容器
            generate_mac_container_config "$CONTAINER_NAME" "$CONTAINER_COUNT" "$EMAIL_EASY_CONFIG" "standard"
            
            # 记录容器信息
            echo "$CONTAINER_NAME: $EMAIL_PART (Mac)" >> container_info/container_list.txt
            
            # 启动当前容器
            echo -e "${YELLOW}正在启动Mac容器 $CONTAINER_NAME...${NC}"
            $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d $CONTAINER_NAME
            
            # 检查容器是否成功启动
            sleep 2
            if docker ps | grep -q "$CONTAINER_NAME"; then
                echo -e "${GREEN}Mac容器 $CONTAINER_NAME 启动成功!${NC}"
                
                # 增加计数器
                CONTAINER_COUNT=$((CONTAINER_COUNT+1))
                
                # 显示当前容器列表
                echo -e "${BLUE}当前Mac容器列表:${NC}"
                docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
                
                echo -e "${GREEN}Mac容器 #$CONTAINER_COUNT 已添加并启动${NC}"
            else
                echo -e "${RED}错误: Mac容器 $CONTAINER_NAME 启动失败${NC}"
                # 删除失败的容器配置
                echo -e "${YELLOW}正在清理失败的容器配置...${NC}"
                $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE down $CONTAINER_NAME 2>/dev/null || true
                docker rm -f $CONTAINER_NAME 2>/dev/null || true
                # 删除容器记录
                sed -i "/$CONTAINER_NAME:/d" container_info/container_list.txt 2>/dev/null || true
            fi
        done
    fi
fi

# 显示总结信息
if [ $CONTAINER_COUNT -gt 0 ]; then
    echo -e "\n${PURPLE}=========================================${NC}"
    echo -e "${GREEN}Mac容器部署完成!${NC}"
    echo -e "${PURPLE}=========================================${NC}"
    echo -e "${CYAN}已成功部署 $CONTAINER_COUNT 个Mac优化容器${NC}"
    echo -e "${CYAN}系统架构: $PLATFORM${NC}"
    echo -e "${CYAN}Docker平台: $DOCKER_PLATFORM${NC}"
    echo -e "${CYAN}内存限制: 每容器最大使用 $MEMORY_LIMIT${NC}"
    
    # 等待容器启动
    echo -e "\n${YELLOW}等待容器启动完成...${NC}"
    sleep 5
    
    # 显示容器状态
    echo -e "\n${BLUE}Mac容器运行状态:${NC}"
    docker ps --filter "name=openai-regflow" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" || true
    
    # Mac专用管理提示
    echo -e "\n${YELLOW}Mac专用管理命令:${NC}"
    echo -e "${BLUE}查看容器状态:${NC} docker ps --filter 'name=openai-regflow'"
    echo -e "${BLUE}查看容器日志:${NC} docker logs -f <容器名>"
    echo -e "${BLUE}停止所有容器:${NC} $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE down"
    echo -e "${BLUE}启动所有容器:${NC} $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE up -d"
    echo -e "${BLUE}重启容器:${NC} $DOCKER_COMPOSE_CMD -f $MAIN_COMPOSE_FILE restart"
    echo -e "${BLUE}更新镜像:${NC} ./start_many_mac.sh update"
    
    # Mac性能监控提示
    echo -e "\n${CYAN}Mac性能监控:${NC}"
    echo -e "${BLUE}检查Docker资源使用:${NC} docker stats"
    echo -e "${BLUE}检查Mac系统资源:${NC} top -o cpu"
    echo -e "${BLUE}Docker Desktop Dashboard:${NC} 打开Docker Desktop查看详情"
    
    echo -e "\n${GREEN}Mac优化部署完成！容器已针对您的 $PLATFORM 系统优化${NC}"
    echo -e "${PURPLE}=========================================${NC}"
else
    echo -e "\n${YELLOW}未添加任何容器${NC}"
fi 